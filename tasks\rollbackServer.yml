---

- name: "check  {{ rollback_server }}是否安装"
  stat: 
    path: "/home/<USER>/server/{{ rollback_server }}/bin/.env"
  register: check_server
  ignore_errors: True

- name: "停止{{ rollback_server }}服务"
  command: "ls"
  notify:
    - stop {{ rollback_server }}
  when:
    - check_server.stat.exists == True
  ignore_errors: True

- name: "查找指定文件备份"
  find:
    recurse: no
    paths:
      - "/home/<USER>/backups"
    patterns:
      - '{{ rollback_server }}.bk-*'
    hidden: yes
    file_type: any
    use_regex: true
  register: backup_server

- name: 查找备份文件 #查找最后一个备份文件按照时间排序
  set_fact:
    latest_backup: "{{ (backup_server.files | sort(attribute='mtime') | last).path }}"

- name: 输出backup_server
  debug: 
    msg: "{{ backup_server.files | map(attribute='path') | list }}" 

- name: 输出latest_backup
  debug: 
    msg: "{{ latest_backup }}"

- name: drop {{ rollback_server }}
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/home/<USER>/server/{{ rollback_server }}"
  when:
    - check_server.stat.exists == True
  ignore_errors: True

- name: 还原{{ rollback_server }}服务
  command: "cp -a {{ latest_backup }} /home/<USER>/server/{{ rollback_server }}"
  notify:
    - restart {{ rollback_server }}
  ignore_errors: True

- name: 提前重启服务
  meta: flush_handlers
  ignore_errors: True
