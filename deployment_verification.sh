#!/bin/bash

# 东莞银行风险管控系统部署验证脚本
# 部署用户: appuser
# 部署目录: /export

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 验证结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_item() {
    local description="$1"
    local command="$2"
    local expected_result="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    printf "%-50s " "$description"
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 通过${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        echo -e "${RED}✗ 失败${NC}"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 检查目录存在
check_directory() {
    local dir="$1"
    local description="$2"
    check_item "$description" "[ -d '$dir' ]"
}

# 检查文件存在
check_file() {
    local file="$1"
    local description="$2"
    check_item "$description" "[ -f '$file' ]"
}

# 检查服务运行
check_service() {
    local service="$1"
    local description="$2"
    check_item "$description" "systemctl is-active $service"
}

# 检查端口监听
check_port() {
    local port="$1"
    local description="$2"
    check_item "$description" "netstat -tln | grep ':$port '"
}

# 检查用户存在
check_user() {
    local user="$1"
    local description="$2"
    check_item "$description" "id $user"
}

echo "=========================================="
echo "东莞银行风险管控系统部署验证"
echo "验证时间: $(date)"
echo "=========================================="

echo
echo -e "${BLUE}=== 1. 系统环境检查 ===${NC}"
check_item "操作系统版本" "cat /etc/redhat-release | grep -E '(CentOS|Red Hat)'"
check_item "内核版本" "uname -r"
check_item "防火墙状态 (应为 inactive)" "! systemctl is-active firewalld"
check_item "SELinux 状态 (应为 disabled)" "getenforce | grep -i disabled"

echo
echo -e "${BLUE}=== 2. 用户和权限检查 ===${NC}"
check_user "appuser" "用户 appuser 存在"
check_item "用户 appuser 属于 docker 组" "groups appuser | grep docker"
check_item "用户 appuser 属于 agent 组" "groups appuser | grep agent"
check_item "/export 目录属主为 appuser" "[ \$(stat -c '%U' /export) = 'appuser' ]"

echo
echo -e "${BLUE}=== 3. 目录结构检查 ===${NC}"
check_directory "/export" "/export 根目录"
check_directory "/export/tools" "/export/tools 工具目录"
check_directory "/export/app" "/export/app 应用目录"
check_directory "/export/bin" "/export/bin 脚本目录"
check_directory "/export/config" "/export/config 配置目录"
check_directory "/export/data" "/export/data 数据目录"
check_directory "/export/logs" "/export/logs 日志目录"
check_directory "/export/server" "/export/server 服务器目录"
check_directory "/export/source" "/export/source 源码目录"

echo
echo -e "${BLUE}=== 4. Java 环境检查 ===${NC}"
check_directory "/export/tools/jdk" "JDK 8 安装目录"
check_file "/export/tools/jdk/bin/java" "JDK 8 可执行文件"
check_file "/home/<USER>/.bash_profile" "用户环境变量配置"

echo
echo -e "${BLUE}=== 5. 基础服务检查 ===${NC}"
check_service "postgresql-13" "PostgreSQL 数据库服务"
check_service "redis" "Redis 缓存服务"
check_port "5432" "PostgreSQL 端口 5432"
check_port "6379" "Redis 端口 6379"

echo
echo -e "${BLUE}=== 6. 中间件服务检查 ===${NC}"
check_directory "/export/server/zookeeper" "Zookeeper 安装目录"
check_directory "/export/server/kafka" "Kafka 安装目录"
check_file "/export/server/zookeeper/bin/service.sh" "Zookeeper 服务脚本"
check_file "/export/server/kafka/bin/service.sh" "Kafka 服务脚本"

# 如果 Zookeeper 和 Kafka 已启动，检查端口
if systemctl is-active zookeeper >/dev/null 2>&1 || netstat -tln | grep ":2181 " >/dev/null; then
    check_port "2181" "Zookeeper 端口 2181"
fi

if systemctl is-active kafka >/dev/null 2>&1 || netstat -tln | grep ":9092 " >/dev/null; then
    check_port "9092" "Kafka 端口 9092"
fi

echo
echo -e "${BLUE}=== 7. 应用服务检查 ===${NC}"
APPS=("init:8081" "receiver:8082" "cleaner:8083" "transfer:8080" "threat:8084" "web-service:8085")

for app_config in "${APPS[@]}"; do
    app_name=$(echo "$app_config" | cut -d: -f1)
    app_port=$(echo "$app_config" | cut -d: -f2)
    
    check_directory "/export/app/$app_name" "$app_name 应用目录"
    check_file "/export/app/$app_name/bin/service.sh" "$app_name 服务脚本"
    
    # 如果应用已启动，检查端口
    if netstat -tln | grep ":$app_port " >/dev/null; then
        check_port "$app_port" "$app_name 端口 $app_port"
    fi
done

echo
echo -e "${BLUE}=== 8. 管理脚本检查 ===${NC}"
check_file "/export/bin/manage_services.sh" "服务管理脚本"
check_file "/export/bin/check_services.sh" "服务检查脚本"
check_item "manage_services.sh 可执行" "[ -x '/export/bin/manage_services.sh' ]"
check_item "check_services.sh 可执行" "[ -x '/export/bin/check_services.sh' ]"

echo
echo -e "${BLUE}=== 9. 网络连接检查 ===${NC}"
check_item "本地回环接口" "ping -c 1 127.0.0.1"
check_item "DNS 解析" "nslookup baidu.com"

# 检查主机名解析 (如果配置了集群)
if grep -q "sh.bdc_tom.a\|sh.bdc_tom.b\|hl.bdc_tom.a" /etc/hosts; then
    check_item "集群主机名解析" "grep -E 'sh.bdc_tom.a|sh.bdc_tom.b|hl.bdc_tom.a' /etc/hosts"
fi

echo
echo -e "${BLUE}=== 10. 系统资源检查 ===${NC}"
check_item "磁盘空间充足 (>10GB)" "[ \$(df /export | tail -1 | awk '{print \$4}') -gt 10485760 ]"
check_item "内存充足 (>4GB)" "[ \$(free -m | grep '^Mem:' | awk '{print \$2}') -gt 4096 ]"
check_item "CPU 核心数 (>2)" "[ \$(nproc) -gt 2 ]"

echo
echo "=========================================="
echo -e "${BLUE}验证结果摘要${NC}"
echo "=========================================="
echo -e "总检查项: ${BLUE}$TOTAL_CHECKS${NC}"
echo -e "通过项目: ${GREEN}$PASSED_CHECKS${NC}"
echo -e "失败项目: ${RED}$FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo
    echo -e "${GREEN}🎉 所有检查项目都通过了！${NC}"
    echo -e "${GREEN}系统已准备好进行应用部署。${NC}"
    exit 0
else
    echo
    echo -e "${RED}⚠️  有 $FAILED_CHECKS 个检查项目失败。${NC}"
    echo -e "${YELLOW}请根据失败项目进行相应的修复。${NC}"
    
    echo
    echo -e "${BLUE}常见问题解决方案:${NC}"
    echo "1. 如果用户或目录不存在，请运行快速部署脚本"
    echo "2. 如果 Java 环境检查失败，请手动安装 JDK"
    echo "3. 如果服务检查失败，请检查服务配置和启动状态"
    echo "4. 如果端口检查失败，请确认服务已正确启动"
    
    exit 1
fi
