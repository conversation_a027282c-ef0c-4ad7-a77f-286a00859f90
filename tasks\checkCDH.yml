---
#- tags: backupALLServer
- import_tasks: createUser.yml

#- name: copy rpm包
#  copy:
#    src: "{{ item }}"
#    dest: /tmp/rpms/
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    mode: 0644
#  with_fileglob:
#    - "tools/rpms/*.rpm"
#  register: rpms_copied

#- name: 输出rpms
#  debug:
#    msg: "{{ rpms_copied }}"

#- name: local RPMs not found
#  fail:
#    msg: "RPMs not found in ../files/"
#  when: rpms_copied.results|length == 0 and rpms_copied.skipped and rpms_copied.skipped_reason.find('No items') != -1

#- name: rpm包数据化
#  set_fact:
#    rpm_list: "{{ rpms_copied.results | map(attribute='dest') | list}}"

#- name: install RPMs
#  yum:
#    name: "{{rpm_list}}"

- name: 清除cdh的xml文件缓存
  file:
    path: /tmp/4602/
    force: yes
    state: absent
  ignore_errors: True

- name: check  /etc/kafka/conf/kafka-client.conf
  stat: 
    path: "/etc/kafka/conf/kafka-client.conf"
  register: check_zk
  ignore_errors: True

- name: 输出zookeeper地址
  debug:
    msg: "{{ check_zk }}"

- name: 获取zookeeper配置文件
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "/tmp/4602/"
    rsync_timeout: 600
  ignore_errors: True
  with_items:
    - "/etc/kafka/conf/kafka-client.conf"
  when: check_zk.stat.exists == True

- name: 获取zookeeper地址
  shell:  "cat /tmp/4602/kafka-client.conf | awk -F '=' '{print $2}'"
  register: zookeeper_list
  connection: local
  when: check_zk.stat.exists == True

- name: 输出zookeeper地址
  debug:
    msg: "{{ zookeeper_list }}"
  when: check_zk.stat.exists == True

- name: 将zookeeper地址全局变量
  set_fact:
    zookeeper_addr: "{{ zookeeper_list.stdout }}"
  when: check_zk.stat.exists == True

- name: 输出zookeeper地址
  debug:
    msg: "{{ zookeeper_addr }}"
  when: check_zk.stat.exists == True

- name: check  /home/<USER>/transfer
  stat: 
    path: "/home/<USER>/transfer"
  register: check_transfer
  ignore_errors: True

- name: 获取transfer配置文件
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "/tmp/4602/"
    rsync_timeout: 600
  ignore_errors: True
  with_items:
    - "/home/<USER>/transfer/config/core-site.xml"
    - "/home/<USER>/transfer/config/hbase-site.xml"
    - "/home/<USER>/transfer/config/hdfs-site.xml"
  when: check_transfer.stat.exists == True

- name: check  /etc/hadoop/conf
  stat: 
    path: "/etc/hadoop/conf"
  register: check_hadoop
  ignore_errors: True

- name: 获取hadoop配置文件
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "/tmp/4602/"
    rsync_timeout: 600
  ignore_errors: True
  with_items:
    - "/etc/hadoop/conf/core-site.xml"
    - "/etc/hadoop/conf/hdfs-site.xml"
  when: check_hadoop.stat.exists == True

- name: check  /etc/hbase/conf
  stat: 
    path: "/etc/hbase/conf"
  register: check_hbase
  ignore_errors: True

- name: 获取hbase配置文件
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "/tmp/4602/"
    rsync_timeout: 600
  ignore_errors: True
  with_items:
    - "/etc/hbase/conf/core-site.xml"
    - "/etc/hbase/conf/hbase-site.xml"
  when: check_hbase.stat.exists == True

- name: 给cdh集群打个tag
  set_fact: 
    cdh_tag: True
  when: check_transfer.stat.exists == True or
        check_hadoop.stat.exists == True or
        check_hbase.stat.exists == True

- name: 输出cdh集群tag
  debug:
    msg: "{{ cdh_tag }}"
  when: check_transfer.stat.exists == True or
        check_hadoop.stat.exists == True or
        check_hbase.stat.exists == True
