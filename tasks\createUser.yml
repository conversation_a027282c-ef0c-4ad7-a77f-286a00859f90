---
#- tags: 创建用户
- name: check {{ bangcle_user }}是否设置完成
  stat: 
    path: "/home/<USER>/tools/jdk/bin/java"
  register: check_bangcle_user

- name: 输出java
  debug:
    msg: "{{ check_bangcle_user }}"

#- name: 用户初始化完成
#  meta: end_play
#  when: check_bangcle_user.stat.exists == True

#- name: set distribution type
#  set_fact:
#    distribution_type: "{{ ansible_distribution.split(' ')[0]|lower }}-{{ ansible_distribution_major_version|replace('\"','')|lower }}-{{ ansible_architecture|lower }}"

- name: "查看最大空间目录"
  shell: "df |grep -v 'nfs' | grep '/'|sort -k 4 -n |awk '{if($NF!~\"/run|/sys|/run|/dev|/boot|/tmp|/var|/usr|docker\")print $NF}'| tail -n 1"
  register: disk_out

- name : 最大空间目录为{{ disk_out.stdout }}
  debug: var=disk_out.stdout verbosity=0
  when: check_bangcle_user.stat.exists == False

- name: 创建一个docker的group组
  group:
    name: docker
    state: present
  when: check_bangcle_user.stat.exists == False

- name: 创建一个{{ bangcle_user }}的group组
  group:
    name: "{{ bangcle_user }}"
    state: present
  when: check_bangcle_user.stat.exists == False and ansible_distribution.split(' ')[0]|lower == 'sles'

- name: python2 set user passwd
  shell: python -c "import hashlib; print(hashlib.sha512(b'{{ bangcle_password }}').hexdigest())"
  register: hashed_password
  ignore_errors: true

- name: python3 set user passwd
  shell: python3 -c "import hashlib; print(hashlib.sha512(b'{{ bangcle_password }}').hexdigest())"
  register: hashed_password
  when: hashed_password.rc != 0

- name:  创建用户于最大目录/home
  user:
    name: "{{ bangcle_user }}"
    password: "{{ bangcle_password|password_hash('sha512') }}"
    home: "/home/<USER>"
    state: present
    groups: docker
    append: yes
    shell: /bin/bash
    update_password: always
  when: disk_out.stdout == '/' and check_bangcle_user.stat.exists == False

- name:  创建用户于最大目录"{{ disk_out.stdout }}""
  user:
    name: "{{ bangcle_user }}"
    password: "{{ bangcle_password|password_hash('sha512') }}"
    home: "{{ disk_out.stdout }}/{{ bangcle_user }}"
    state: present
    groups: docker
    append: yes
    shell: /bin/bash
    update_password: always
  when: disk_out.stdout != '/' and check_bangcle_user.stat.exists == False

- name: 用户软连接到/home/<USER>
  file:
    src: "{{ disk_out.stdout }}/{{ bangcle_user }}"
    dest: "/home/<USER>"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
  when: disk_out.stdout != '/' and disk_out.stdout != '/home' and check_bangcle_user.stat.exists == False

- name: 创建部署所需要的目录
  file:
    path: "/home/<USER>/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - tools
    - app
    - server
    - backups
    - bin
    - ops
    - data/web-service
    - data/transfer
    - tmp
    - td
    - .ssh
  when: check_bangcle_user.stat.exists == False

# - name: 配置生成密钥脚本
#   template:
#     src: bangcleUser/init_key.sh.j2
#     dest: /tmp/init_key.sh
#   connection: local

- name: 配置gpg
  template:
    src: bangcleUser/{{ item }}
    dest: /tmp/{{ item }}
  with_items:
    - private-key.txt
    - public-key.txt
  connection: local
  when:
    - ansible_architecture|lower == "x86_64"

- name: 生成密钥
  become_user: "{{ ansible_ssh_user }}"
  shell: "bash /tmp/init_key.sh"
  ignore_errors: True
  connection: local

- name: 生成密钥
  shell: "bash /tmp/init_key.sh"
  ignore_errors: True
  connection: local

- name: 添加认证至远程主机
  authorized_key:
    user: "{{ bangcle_user }}"
    state: present
    key: "{{ lookup('file', '/root/.ssh/id_rsa.pub') }}"
  ignore_errors: True

- name: 添加认证至远程主机
  authorized_key:
    user: "{{ ansible_ssh_user }}"
    state: present
    key: "{{ lookup('file', '/home/<USER>/.ssh/id_rsa.pub') }}"
  ignore_errors: True

# - name: copy 密钥文件
#   copy:
#     src: "{{ item }}"
#     dest: /home/<USER>/.ssh/
#     owner: "{{ bangcle_user }}"
#     group: "{{ bangcle_user }}"
#     force: yes
#     mode: 0600
#   with_fileglob:
#     - "/root/.ssh/id_rsa"
#     - "/root/.ssh/id_rsa.pub"
#     - "/root/.ssh/authorized_keys"
#     - "/home/<USER>/.ssh/id_rsa"
#     - "/home/<USER>/.ssh/id_rsa.pub"
#     - "/home/<USER>/.ssh/authorized_keys"
#   ignore_errors: True

- name: 配置bangcle密钥
  template:
    src: "bangcleUser/ssh/{{ item.key }}"
    dest: "/home/<USER>/.ssh/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0600
  with_items:
    - { key: 'id_rsa.j2' , value: 'id_rsa' }
    - { key: 'id_rsa.pub.j2' , value: 'id_rsa.pub' }
    - { key: 'authorized_keys.j2' , value: 'authorized_keys' }

- name: 传jdk
  unarchive:
    src: "tools/jdk/{{ ansible_architecture|lower }}/{{ jdk_tarball_name }}"
    dest: "/home/<USER>/tools"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_bangcle_user.stat.exists == False

- name: jdk做软连接
  file:
    src: "/home/<USER>/tools/{{ jdk_name }}"
    dest: "/home/<USER>/tools/jdk"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: check_bangcle_user.stat.exists == False

- name: 传openjdk17u
  unarchive:
    src: "tools/jdk/{{ ansible_architecture|lower }}/{{ openjdk_tarball_name }}"
    dest: "/home/<USER>/tools"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_bangcle_user.stat.exists == False

- name: openjdk做软连接
  file:
    src: "/home/<USER>/tools/{{ openjdk_name }}"
    dest: "/home/<USER>/tools/jdk-17"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: check_bangcle_user.stat.exists == False

- name: 配置环境变量
  template:
    src: bangcleUser/bash_profile.j2
    dest: /home/<USER>/.bash_profile
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_bangcle_user.stat.exists == False

#- name: 配置umask
#  shell: "echo \"umask 0002\" >> /home/<USER>/.bash_profile"
#  when:
#    - check_bangcle_user.stat.exists == False
#    - ansible_architecture|lower == "aarch64"

- name: 配置sudo配置
  template:
    src: bangcleUser/bangcle_sudo.j2
    dest: /etc/sudoers.d/{{ bangcle_user }}_sudo
  when: 
    - check_bangcle_user.stat.exists == False
    - bangcle_sudomin == False

- name: 配置sudo配置
  template:
    src: bangcleUser/bangcle_sudomin.j2
    dest: /etc/sudoers.d/{{ bangcle_user }}_sudo
  when: 
    - check_bangcle_user.stat.exists == False
    - bangcle_sudomin == True

- name: 调整系统文件sysctl.conf
  lineinfile:
    dest: "/etc/sysctl.conf"
    regexp: "^{{ item.key }}"
    line: "{{ item.value }}"
  with_items:
    - { key: 'fs.file-max=' , value: 'fs.file-max=2048000' }
    - { key: 'net.ipv4.tcp_tw_reuse=' , value: 'net.ipv4.tcp_tw_reuse=1' }
    - { key: 'vm.swappiness=' , value: 'vm.swappiness=0' }
    - { key: 'vm.max_map_count=' , value: 'vm.max_map_count=262144' }
    - { key: 'net.ipv4.ip_forward=' , value: 'net.ipv4.ip_forward=1' }
    - { key: 'net.ipv6.conf.all.forwarding=' , value: 'net.ipv6.conf.all.forwarding=1' }
    - { key: 'net.ipv4.tcp_fin_timeout=' , value: 'net.ipv4.tcp_fin_timeout=10' }
    - { key: 'net.ipv4.tcp_tw_recycle=' , value: 'net.ipv4.tcp_tw_recycle=0' }
  when: check_bangcle_user.stat.exists == False

#- name: Extract kernel version
#  set_fact:
#    kernel_version: "{{ ansible_kernel.split('-')[0] }}"

## 设置net.ipv4.tcp_tw_recycle=0，因为1的时候mac无法访问kibana
#- name: 内核低于4.0时，调整tcp参数
#  lineinfile:
#    dest: "/etc/sysctl.conf"
#    regexp: "^{{ item.key }}"
#    line: "{{ item.value }}"
#  with_items:
#    - { key: 'net.ipv4.tcp_tw_recycle=' , value: 'net.ipv4.tcp_tw_recycle=1' }
#  when:
#    - kernel_version | string < '4.0'

- name: 系统配置文件sysctl生效
  command: "sysctl -p"
  ignore_errors: True
  when: check_bangcle_user.stat.exists == False

- name: 调整用户文件limits.conf
  lineinfile: 
    dest: "/etc/security/limits.conf"
    regexp: "^{{ item.key }}"
    line: "{{ item.value }}"
  with_items:
    - { key: 'bangcle soft nofile ' , value: 'bangcle soft nofile 65536' }
    - { key: 'bangcle hard nofile' , value: 'bangcle hard nofile 65536' }
    - { key: 'bangcle soft memlock' , value: 'bangcle soft memlock unlimited' }
    - { key: 'bangcle hard memlock' , value: 'bangcle hard memlock unlimited' }
    - { key: '\* soft nproc' , value: '* soft nproc 65536' }
    - { key: '\* - nofile' , value: '* - nofile 65536' }
  when: check_bangcle_user.stat.exists == False

- name: check /etc/security/limits.d/20-nproc.conf 是否存在
  stat: 
    path: "/etc/security/limits.d/20-nproc.conf"
  register: check_20nproc

- name: 调整用户文件20-nproc
  lineinfile: 
    dest: "/etc/security/limits.d/20-nproc.conf"
    regexp: "^{{ item.key }}"
    line: "{{ item.value }}"
  with_items:
    - { key: 'bangcle soft nofile ' , value: 'bangcle soft nofile 65536' }
    - { key: 'bangcle hard nofile' , value: 'bangcle hard nofile 65536' }
    - { key: 'bangcle soft memlock' , value: 'bangcle soft memlock unlimited' }
    - { key: 'bangcle hard memlock' , value: 'bangcle hard memlock unlimited' }
    - { key: '\* soft nproc' , value: '* soft nproc 65536' }
    - { key: '\* - nofile' , value: '* - nofile 65536' }
  when: 
    - check_bangcle_user.stat.exists == False
    - check_20nproc.stat.exists == True

#关闭防火墙和selinux
#关闭防火墙
- name: 停止firewalld
  service:
    name: firewalld
    state: stopped
    enabled: no
  when: check_bangcle_user.stat.exists == False
  ignore_errors: True
  
#关闭selinux
- name: setenforce 0
  shell: "setenforce 0"
  failed_when: false
  when: check_bangcle_user.stat.exists == False

- name: set selinux disabled
  replace:
    path: /etc/selinux/config
    regexp: '^SELINUX=enforcing'
    replace: 'SELINUX=disabled'
  when: check_bangcle_user.stat.exists == False
  ignore_errors: True

## 威胁感知整体服务脚本
- name: 映射整体服务配置文件 
  template:
    src: "bangcleUser/{{ item.key }}"
    dest: "/home/<USER>/bin/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service_all.sh.j2', value: 'service_all.sh' }
    - { key: 'service_app_all.sh.j2' , value: 'service_app_all.sh' }
    - { key: 'service_server_all.sh.j2' , value: 'service_server_all.sh' }
    - { key: 'service_check_all.sh.j2' , value: 'service_check_all.sh' }
    - { key: 'service_ops_all.sh.j2' , value: 'service_ops_all.sh' }
  # when: check_bangcle_user.stat.exists == False

- name: 配置停止服务器前优先停止服务
  template:
    src: "bangcleUser/stopSrv.service.j2"
    dest: "/etc/systemd/system/stopSrv.service"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  notify:
    - enable service stopSrv

- name: 调整/etc/rc.local
  lineinfile: 
    path: "/etc/rc.local"
    line: "su - {{ bangcle_user }} -c \"./bin/service_all.sh start\""
    mode: 0755
  when: check_bangcle_user.stat.exists == False and supervisord_used == false
  ignore_errors: True

- name: delete line like "su - bangcle ......" if supervisord_used is true
  lineinfile: 
    path: "/etc/rc.local"
    line: "su - {{ bangcle_user }} -c "
    state: absent
  when: check_bangcle_user.stat.exists == False and supervisord_used == true
  ignore_errors: True

- name: update mode /etc/rc.d/rc.local
  shell: chmod 755 /etc/rc.d/rc.local
  when: check_bangcle_user.stat.exists == False
  ignore_errors: True

#- name: set include tasks name
#  set_fact:
#    installswfile: "install-sw-{{ distribution_type }}.yml"
#  when: check_bangcle_user.stat.exists == False

#- name: cat file name
#  debug:
#    msg: "{{ installswfile }}"
#  when: check_bangcle_user.stat.exists == False

## 目前都改为镜像容器启动，操作系统无需再安装lsof命令
#- include_tasks: "{{ installswfile }}"
#  when: 
#    - check_bangcle_user.stat.exists == False
#    - ansible_architecture|lower == "x86_64"
