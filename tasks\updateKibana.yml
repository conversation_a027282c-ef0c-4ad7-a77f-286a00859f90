# tags: update_Kibana
- name: 停止kibana服务
  become_user: "{{ bangcle_user }}"
  shell: "bash /home/<USER>/server/kibana/bin/service.sh stop && sleep 20"
  ignore_errors: True

- name: 判断部署目录是否有6.8.5版本
  stat:
    path: "/home/<USER>/server/kibana-6.8.5"
  register: kibana_status
  
- name: 备份kibana服务程序文件为6.0.0版本
  become_user: "{{ bangcle_user }}"
  shell: "mv /home/<USER>/server/kibana{,-6.0.0}"
  ignore_errors: True
  when: kibana_status.stat.exists != True

- name: 拷贝新版本6.8.5到运行目录下
  unarchive:
    src: "server/kibana/{{ ansible_architecture|lower }}/{{ kibanaXpack_tarball_name }}"
    dest: "/home/<USER>/server"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  when: kibana_status.stat.exists != True

- name: kibana软连接到/home/<USER>/server/kibana
  file:
    src: "/home/<USER>/server/kibana-6.8.5"
    dest: "/home/<USER>/server/kibana"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: kibana_status.stat.exists != True

- name: 将kibana原配置文件拷贝到新版本中,避免原配置文件已优化
  become_user: "{{ bangcle_user }}"
  shell: "cp -f /home/<USER>/server/kibana-6.0.0/config/*  /home/<USER>/server/kibana/config/"
  when: kibana_status.stat.exists != True

- name: 拷贝kibana启动文件到kibana/bin/
  become_user: "{{ bangcle_user }}"
  shell: "cp -f /home/<USER>/server/kibana-6.0.0/bin/{service.sh,startup.sh,bangcle_kibana_dog.sh} /home/<USER>/server/kibana/bin/"
  when: kibana_status.stat.exists != True

- name: chmod kibana bin
  shell: "chmod -R 755 /home/<USER>/server/kibana/bin"
  when: kibana_status.stat.exists != True

- name: 在kibana配置文件增加用户认证
  lineinfile:
    path: /home/<USER>/server/kibana/config/kibana.yml
    line: "{{ item }}"
  with_items:
    - 'elasticsearch.username: "elastic"'
    - 'elasticsearch.password: "beap123"'

- name: 启动kibana服务
  shell: "ls -al"
  notify:
    - restart kibana
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
