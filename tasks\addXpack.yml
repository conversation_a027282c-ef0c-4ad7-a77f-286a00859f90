### 升级update_elasticsearchMaster
- import_tasks: updateElasticsearchMaster.yml
  tags:
    - update_ElasticsearchMaster
  when: inventory_hostname in groups['elasticsearchMaster']

### 升级update_elasticsearchclient
- import_tasks: updateElasticsearchClient.yml
  tags:
    - update_ElasticsearchClient
  when: inventory_hostname in groups['elasticsearchClient']

# ### 判断elasticsearch集群状态并设置 elastic用户密码
# - import_tasks: updateElasticsearchSecurity.yml
#   tags:
#     - update_ElasticsearchMaster
#   when: inventory_hostname == groups['elasticsearchMaster'][0]

### 升级update_kibana
- import_tasks: updateKibana.yml
  tags:
    - update_Kibana
    - new
  when: inventory_hostname in groups['kibana']