# deploy_app_service 函数改进说明

## 📋 改进概述

已对 `东莞银行定制化部署指南.md` 中的 `deploy_app_service` 函数进行了全面优化和增强，使其更加适合东莞银行的生产环境部署需求。

## 🔧 主要改进内容

### 1. 增强的部署流程
- ✅ 添加了详细的部署进度显示
- ✅ 增加了备份机制，避免覆盖现有部署
- ✅ 添加了应用包存在性检查
- ✅ 支持从 `/export/source/` 目录读取应用包

### 2. 完善的配置文件
**application.properties 增强：**
- 添加了完整的 TDSQL 数据库连接池配置
- 增加了 Kafka 生产者和消费者详细配置
- 添加了 Redis 缓存配置
- 增加了 Spring Boot Actuator 监控配置
- 添加了应用特定配置项

### 3. 专业的启动脚本
**service.sh 脚本增强：**
- 使用颜色输出，提升用户体验
- 添加了完整的 JVM 参数配置（G1GC、GC日志、堆转储等）
- 实现了优雅停止机制（先 SIGTERM，超时后 SIGKILL）
- 增加了进程状态检查和端口监听检查
- 添加了内存使用情况显示
- 支持实时日志查看功能

### 4. 完整的日志配置
**log4j2.xml 配置：**
- 分离了普通日志和错误日志
- 配置了日志轮转和压缩
- 设置了不同组件的日志级别
- 支持控制台和文件双重输出

### 5. 部署验证机制
- 添加了部署后的自动验证脚本
- 检查目录结构、脚本权限、配置文件
- 提供了清晰的部署状态反馈

## 📁 生成的文件结构

每个服务部署后会生成以下完整的目录结构：

```
/export/app/[service_name]/
├── bin/
│   └── service.sh              # 增强的服务管理脚本
├── config/
│   ├── application.properties  # 完整的应用配置
│   └── log4j2.xml             # 日志配置文件
├── lib/
│   └── [service_name].jar     # 应用 JAR 包
├── log/                       # 日志目录
├── data/                      # 数据目录
└── temp/                      # 临时文件目录
```

## 🚀 使用方法

### 1. 部署单个服务
```bash
# 部署 init 服务，端口 8081，内存 1g
deploy_app_service "init" "8081" "1g"
```

### 2. 服务管理命令
```bash
# 启动服务
/export/app/init/bin/service.sh start

# 停止服务
/export/app/init/bin/service.sh stop

# 重启服务
/export/app/init/bin/service.sh restart

# 查看状态
/export/app/init/bin/service.sh status

# 查看日志
/export/app/init/bin/service.sh logs
```

### 3. 快捷管理命令
```bash
# 使用符号链接的快捷命令
/export/bin/init-service.sh start
/export/bin/receiver-service.sh status
```

## ⚙️ 配置要点

### 1. TDSQL 数据库配置
需要修改以下配置项：
```properties
spring.datasource.url=*****************************************
spring.datasource.username=everisk
spring.datasource.password=everisk123
```

### 2. Kafka 集群配置
```properties
spring.kafka.bootstrap-servers=sh.bdc_tom.a:9092,sh.bdc_tom.b:9092,hl.bdc_tom.a:9092
```

### 3. Redis 缓存配置
```properties
spring.redis.host=sh.bdc_tom.a
spring.redis.port=6379
spring.redis.password=redis123
```

## 🔍 监控和诊断

### 1. 服务状态监控
- 进程 PID 检查
- 端口监听状态
- 内存使用情况
- JVM GC 日志

### 2. 健康检查端点
每个服务提供以下监控端点：
- `http://host:port/actuator/health` - 健康状态
- `http://host:port/actuator/info` - 应用信息
- `http://host:port/actuator/metrics` - 性能指标

### 3. 日志文件
- `[service].log` - 应用主日志
- `[service]-error.log` - 错误日志
- `gc-[service].log` - GC 日志

## 🛡️ 安全和权限

### 1. 文件权限
- 所有文件归属 `appuser:appuser`
- 启动脚本具有执行权限
- 配置文件具有适当的读写权限

### 2. 进程安全
- 服务以 `appuser` 用户身份运行
- 避免使用 root 权限
- 支持优雅停止，避免数据丢失

## 📊 性能优化

### 1. JVM 参数优化
- 使用 G1 垃圾收集器
- 设置合理的堆内存大小
- 启用 GC 日志和堆转储
- 配置 GC 暂停时间目标

### 2. 连接池优化
- 数据库连接池参数调优
- Redis 连接池配置
- Kafka 生产者和消费者参数优化

## ⚠️ 注意事项

1. **应用包位置**：确保应用包放在 `/export/source/` 目录
2. **数据库连接**：部署前需要配置正确的 TDSQL 连接信息
3. **端口冲突**：确保指定的端口未被占用
4. **内存分配**：根据服务负载合理分配内存大小
5. **日志空间**：定期清理日志文件，避免磁盘空间不足

## 🔄 后续优化建议

1. 添加服务依赖检查机制
2. 实现自动化健康检查
3. 集成服务发现和注册
4. 添加性能监控告警
5. 实现配置热更新功能

---

**改进完成时间：** $(date)
**适用环境：** 东莞银行生产环境
**版本：** v2.0
