---
# tags: install_cesi

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/ops/cesi/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - conf

- name: copy cesi
  copy:
    src: "ops/cesi/cesi/images"
    dest: "/home/<USER>/ops/cesi"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 安装cesi包
  unarchive:
    src: "{{ cesi_tarball_name }}"
    dest: "/home/<USER>/ops/cesi/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

#- name: restart docker
#  command: "systemctl restart docker"

- name: load backend image
  command: "docker load -i /home/<USER>/ops/cesi/images/cesi-backend.tar"
  
- name: load frontend image
  command: "docker load -i /home/<USER>/ops/cesi/images/cesi-frontend.tar"
  
#- name: restart docker
#  command: "systemctl restart docker"
  
- name: 配置cesi服务配置文件及脚本
  template:
    src: "cesi/{{ item.key }}"
    dest: "/home/<USER>/ops/cesi/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'cesi.conf.j2' , value: 'conf/cesi.conf' }
  notify:
    - restart cesi

- name: 提前重启服务
  meta: flush_handlers
