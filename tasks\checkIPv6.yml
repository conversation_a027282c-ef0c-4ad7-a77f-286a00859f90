---

- name: Gather facts about network interfaces
  setup:
    filter: ansible_*_ipv4

- name: get all ipv4 set
  set_fact:
    allipv4: "{{ ansible_facts | dict2items | selectattr('value.ipv4.address','defined') | list }}"

- name: find interface with specified ip
  set_fact:
    target_interface: "{{ allipv4 | selectattr('value.ipv4.address','match',inventory_hostname) | map(attribute='key') | list | first }}"

- name: Check if the target interface supports IPv6
  set_fact:
    supports_ipv6: "{{ ansible_facts[target_interface].ipv6 is defined and ansible_facts[target_interface].ipv6 | length > 0 }}"

