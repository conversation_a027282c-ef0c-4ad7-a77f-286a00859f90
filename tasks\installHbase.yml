---
# tags: deploy_hbase

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True


- name: check hbase
  stat: 
    path: "/home/<USER>/server/hbase/"
  register: hbase_output

- name: 停止hbase服务
  become_user: "{{ bangcle_user }}"
  command: "bash /home/<USER>/server/hbase/bin/service.sh stop"
  ignore_errors: True
  when:
    - hbase_output.stat.exists == True

- name: 创建hbase的bin目录
  file:
    path: "/home/<USER>/server/hbase/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - data/hbase
    - bin

- name: 从everisk_tag提取版本号
  set_fact:
    wxgz_version: "{{ everisk_tag.split('_')[0].split('ver')[1] }}"

#- name: 比较wxgz_version与hbase_compare_version
#  debug:
#    msg: "wxgz_version 大于或等于 hbase_compare_version"
#  when: wxgz_version is version(hbase_compare_version, '>=')

#- name: 安装hbase包-威胁感知510
#  unarchive:
#    src: "{{ hbase_tarball_name }}"
#    dest: "/home/<USER>/server/hbase"
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    mode: 0755
#  when: wxgz_version is version(hbase_compare_version, '>=')

- name: 安装hbase包-威胁感知低于510版本,安装hbase-1.2.6.1
  unarchive:
    src: "server/hbase-1.2.6.1.tar.gz"
    dest: "/home/<USER>/server/hbase"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
#    force: yes
    mode: 0755
#  when: wxgz_version is version(hbase_compare_version, '<')
  
#- name: hbase软连接到/home/<USER>/server/hbase/hbase
#  file:
#    src: "/home/<USER>/server/hbase/{{ hbase_name }}"
#    dest: "/home/<USER>/server/hbase/hbase"
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    state: link
#    follow: False
#  when: wxgz_version is version(hbase_compare_version, '>=')

- name: hbase软连接到/home/<USER>/server/hbase/hbase
  file:
    src: "/home/<USER>/server/hbase/hbase-1.2.6.1"
    dest: "/home/<USER>/server/hbase/hbase"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
#  when: wxgz_version is version(hbase_compare_version, '<')

#- name: 解压phoenix包
#  unarchive:
#    src: "{{ hbase_phoenix_tarball_name }}"
#    dest: "/home/<USER>/tmp/"
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    mode: 0755
#  ignore_errors: True
#  when: wxgz_version is version(hbase_compare_version, '>=')


#- name: 拷贝phoenix包到hbase的lib目录
#  become_user: "{{ bangcle_user }}"
#  command: "cp -a /home/<USER>/tmp/{{ hbase_phoenix_path }}/{{ hbase_phoenix_name }} /home/<USER>/server/hbase/hbase/lib/ "
#  ignore_errors: True
#  when: wxgz_version is version(hbase_compare_version, '>=')

- name: 配置hbase服务配置文件及脚本
  template:
    src: "hbase/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'hbase-site.xml.j2' , value: 'hbase/hbase/conf/hbase-site.xml' }
    - { key: 'log4j.properties.j2' , value: 'hbase/hbase/conf/log4j.properties' }
    - { key: 'hbase-env.sh.j2' , value: 'hbase/hbase/conf/hbase-env.sh' }
    - { key: 'hbase_dog.sh.j2' , value: 'hbase/bin/hbase_dog.sh' }
    - { key: 'service.sh.j2' , value: 'hbase/bin/service.sh' }
    - { key: 'startup_master.sh.j2' , value: 'hbase/bin/startup_master.sh' }
    - { key: 'startup_regionserver.sh.j2' , value: 'hbase/bin/startup_regionserver.sh' }

- name: 配置HA-hbase服务配置文件及脚本
  template:
    src: "{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'hadoop/hdfs-site.xml.j2' , value: 'hbase/hbase/conf/hdfs-site.xml' }
    - { key: 'hbase/regionservers.j2' , value: 'hbase/hbase/conf/regionservers' }
    - { key: 'hbase/backup-master.j2' , value: 'hbase/hbase/conf/backup-master' }
  when:
    - hbase_ha_tag == True
  
- name: 调整hbase的目录权限
  command: "chown -R {{ bangcle_user }}:{{ bangcle_user }} /home/<USER>/server/hbase/"

- name: 设置hbase master最大堆内存
  template:
    src: "hbase/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'hbase-env-master.sh.j2' , value: 'hbase/hbase/conf/hbase-env-master.sh' }
  when:
    - groups['everisk-deploy'] | length == 1

- name: 设置hbase regioner最大堆内存
  template:
    src: "hbase/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'hbase-env-regionserver.sh.j2' , value: 'hbase/hbase/conf/hbase-env-regionserver.sh' }
  when:
    - groups['everisk-deploy'] | length == 1

## 适配升级
# - name: 检查hbase是否有data目录
#   stat:
#     path: "../backups/4602/hbase/data"
#   register: hbase_data
#   ignore_errors: True
#   connection: local

# - name: 恢复hbase数据
#   copy:
#     src: "../backups/4602/hbase/data/"
#     dest: "/home/<USER>/server/hbase/data/"
#     owner: "{{ bangcle_user }}"
#     group: "{{ bangcle_user }}"
#     force: yes
#     mode: 0755
#   notify:
#     - restart hbase
#   when: hbase_data.stat.exists == True

# elasticsearch数据迁移
# 判断4.6目录
- name: check /bb_home/bb_hbase
  stat:
    path: "/bb_home/bb_hbase"
  register: check_bb_es
  ignore_errors: True

# - name: 数据迁移，将4.6上elasticsearch数据迁移到新目录
#   shell: mv -f /bb_home/bb_hbase/data/* /home/<USER>/server/hbase/data/*
#   when: check_bb_es.stat.exists == True

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/hbase/bin/service.sh"
    path: "/home/<USER>/server/hbase/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/hbase/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "hbase/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/hbase/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart hbase
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
