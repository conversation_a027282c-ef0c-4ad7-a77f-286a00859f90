---

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/ops/portainer/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp

- name: 传包-portainer
  copy:
    src: "{{ portainer_tarball_name}}"
    dest: "/home/<USER>/ops/portainer/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 传包-portainer-agent
  copy:
    src: "{{ portainerAgent_tarball_name}}"
    dest: "/home/<USER>/ops/portainer/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  
- name: 配置portainer的config文件
  template:
    src: "portainer/{{ item.key }}"
    dest: "/home/<USER>/ops/portainer/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'startup_server.sh.j2' , value: 'bin/startup_server.sh' }
    - { key: 'startup_agent.sh.j2' , value: 'bin/startup_agent.sh' }

- name: 加载images
  command: "docker load -i /home/<USER>/ops/portainer/images/portainer.tar"

- name: 加载images agent
  command: "docker load -i /home/<USER>/ops/portainer/images/portainer-agent.tar"


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/ops/portainer/bin/service.sh"
    path: "/home/<USER>/ops/portainer/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/ops/portainer/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "portainer/service_supervisor.sh.j2"
    dest: "/home/<USER>/ops/portainer/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart portainer
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
