---
# ============================================================================
# Ansible 主任务文件 - 整理版
# 用于部署和管理 everisk 系统的各个组件
# ============================================================================

# ============================================================================
# 系统升级和维护任务
# ============================================================================

# 停止所有服务 (用于升级维护)
- import_tasks: stopAllServer.yml
  tags: 
    - never
    - 4.8to4.9
    - stopAll

# 配置文件调整 (4.8 到 4.9 升级)
- import_tasks: 48to49.yml
  tags: 
    - never
    - 4.8to4.9

# ============================================================================
# 基础环境检查和配置
# ============================================================================

# 检查 Hadoop HA 配置
- import_tasks: checkHadoopHA.yml
  tags:
    - install
    - update510
    - check_hadoop_ha
    - always

# 编辑主机名配置
- import_tasks: editHostname.yml
  tags: 
    - install
    - edit_hosts

# ============================================================================
# 基础服务安装 (按依赖顺序排列)
# ============================================================================

## 1. Zookeeper 集群
- import_tasks: installZookeeper.yml
  tags: 
    - install
    - install_zookeeper
  when: 
    - inventory_hostname in groups['zookeeper']

## 2. Hadoop 分布式文件系统
- import_tasks: installHadoop.yml
  tags:
    - install
    - install_hadoop
    - install_hdfs
  when:
    - inventory_hostname in groups['namenode'] or inventory_hostname in groups['datanode']
    - hdfs_tag == True

## 3. Kafka 消息队列
- import_tasks: installKafka.yml
  tags: 
    - install
    - install_kafka
  when: 
    - inventory_hostname in groups['kafka']

## 4. HBase 数据库
- import_tasks: installHbase.yml
  tags: 
    - install
    - install_hbase
    - update_hbase
    - update510
  when: 
    - inventory_hostname in groups['hbase']

# ============================================================================
# 数据存储服务
# ============================================================================

## PostgreSQL 数据库
- import_tasks: installPostgres.yml
  tags: 
    - install
    - install_postgres
  when: inventory_hostname in groups['postgres']

## Redis 缓存
- import_tasks: installRedis.yml
  tags: 
    - install
    - install_redis
  when: inventory_hostname in groups['redis']

## MinIO 对象存储
- import_tasks: installMinio.yml
  tags: 
    - install
    - install_minio
    - 4.8to4.9
  when: inventory_hostname in groups['minio']

# ============================================================================
# 搜索和分析服务
# ============================================================================

## Elasticsearch 客户端节点
- import_tasks: installElasticsearchClient.yml
  tags: 
    - install
    - install_elasticsearchClient
  when: inventory_hostname in groups['elasticsearchClient']

## Elasticsearch 主节点
- import_tasks: installElasticsearchMaster.yml
  tags: 
    - install
    - install_elasticsearchMaster
  when: inventory_hostname in groups['elasticsearchMaster']

## Kibana 可视化
- import_tasks: installKibana.yml
  tags: 
    - install
    - install_kibana
  when: inventory_hostname in groups['kibana']

# ============================================================================
# Web 服务和代理
# ============================================================================

## Nginx 反向代理
- import_tasks: installNginx.yml
  tags: 
    - install
    - install_nginx
  when: inventory_hostname in groups['nginx']

## Crash 服务
- import_tasks: installCrash.yml
  tags: 
    - install
    - install_crash
  when: inventory_hostname in groups['crash']

# ============================================================================
# 容器化环境
# ============================================================================

## Docker 环境更新
- import_tasks: updateDocker.yml
  tags: 
    - never
    - update_docker

## Docker 环境部署
- import_tasks: dockerEnv.yml
  tags: 
    - never
    - deploy_docker

# ============================================================================
# 应用服务部署 (核心业务组件)
# ============================================================================

## 初始化服务
- import_tasks: installInit.yml
  tags: 
    - install
    - update
    - install_app
    - update_app
    - install_init
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['init']

## 数据接收服务
- import_tasks: installReceiver.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_receiver
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['receiver']

## 数据清洗服务
- import_tasks: installCleaner.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_cleaner
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['cleaner']

## 数据传输服务
- import_tasks: installTransfer.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_transfer
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['transfer']

## 威胁检测服务
- import_tasks: installThreat.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_threat
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['threat']

# ============================================================================
# Web 前端和接口服务
# ============================================================================

## Web 服务后端
- import_tasks: installWeb-service.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_web-service
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['web-service']

## Web 服务前端 (Nginx)
- import_tasks: installWeb-service-nginx.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_web-service
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['web-service']

# ============================================================================
# 分析和处理服务
# ============================================================================

## 开发分析器
- import_tasks: installAnalyzer-dev.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_analyzer-dev
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['analyzer-dev']

## 安全事件处理
- import_tasks: installSecurity-event.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_security-event
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['security-event']

# ============================================================================
# 特殊业务组件
# ============================================================================

## 中国人寿账户推送服务
- import_tasks: installChinalife-account-push.yml
  tags:
    - never
    - install_chinalife-account-push
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['chinalife-account-push']

## 应用发送器
- import_tasks: installApp-sender.yml
  tags:  
    - never
    - install
    - install_app-sender
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['app-sender']

# ============================================================================
# 升级和维护服务
# ============================================================================

## 升级服务后端
- import_tasks: installUpgrade-service.yml
  tags:
    - install
    - install_upgrade-service
    - 4.8to4.9
  when:
    - inventory_hostname in groups['upgrade-service']

## 升级服务前端
- import_tasks: installUpgrade-web.yml
  tags:
    - install
    - install_upgrade-web
    - 4.8to4.9
  when:
    - inventory_hostname in groups['upgrade-service']

# ============================================================================
# 监控和运维工具
# ============================================================================

## 系统监控
- import_tasks: installMonitor.yml
  tags:
    - never
    - install
    - install_monitor
  when:
    - ansible_architecture|lower == "x86_64"

## ELK 日志分析栈
- import_tasks: installELK.yml
  tags:
    - never
    - install
    - install_ELK
    - 4.8to4.9
  when: inventory_hostname in groups['ELK']

## Filebeat 日志收集
- import_tasks: installFilebeat.yml
  tags:
    - never
    - install
    - install_filebeat
    - 4.8to4.9

## API 自动化测试
- import_tasks: installApiAutotest.yml
  tags:
    - never
    - install
    - install_api_autotest
  when:
    - inventory_hostname in groups['transfer']
    - ansible_architecture|lower == "x86_64"

# ============================================================================
# 安全和配置管理
# ============================================================================

## Elasticsearch 安全配置
- import_tasks: ElasticsearchSecurity.yml
  tags:
    - install
    - ElasticsearchSecurity
    - add_es_user
  when:
    - ansible_architecture|lower == "x86_64"

## Kafka Topic 配置
- import_tasks: setKafkaTopic.yml
  tags:
    - set_kafka_topic

## 防火墙规则更新
- import_tasks: updateIptables.yml
  tags:
    - never
    - update_iptables

## 系统发行版检查
- import_tasks: checkDistribution.yml
  tags:
    - never
    - check_distribution

# ============================================================================
# 回滚和恢复操作
# ============================================================================

## 初始化服务回滚
- import_tasks: rollbackInit.yml
  vars:
    rollback_app: init
  tags:
    - never
    - rollback
    - rollback_init
  when: inventory_hostname in groups['init']

## 接收服务回滚
- import_tasks: rollbackApp.yml
  vars:
    rollback_app: receiver
  tags:
    - never
    - rollback
    - rollback_receiver
  when: inventory_hostname in groups['receiver']

## 清洗服务回滚
- import_tasks: rollbackApp.yml
  vars:
    rollback_app: cleaner
  tags:
    - never
    - rollback
    - rollback_cleaner
  when: inventory_hostname in groups['cleaner']

## 传输服务回滚
- import_tasks: rollbackApp.yml
  vars:
    rollback_app: transfer
  tags:
    - never
    - rollback
    - rollback_transfer
  when: inventory_hostname in groups['transfer']

## 威胁检测服务回滚
- import_tasks: rollbackApp.yml
  vars:
    rollback_app: threat
  tags:
    - never
    - rollback
    - rollback_threat
  when: inventory_hostname in groups['threat']

# ============================================================================
# 维护和清理操作
# ============================================================================

## 获取初始化配置
- import_tasks: fetchInit.yml
  tags:
    - never
    - fetch
  when: inventory_hostname in groups['init']

## 更新初始化服务
- import_tasks: updateInit.yml
  tags:
    - never
    - update_init
  when: inventory_hostname in groups['init']

## 卸载服务
- import_tasks: unInstallServer.yml
  tags:
    - never
    - cleanup
