---
# tags: deploy_elasticsearchclient

- import_tasks: createUser.yml

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/elasticsearchClient/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - logs
    - data
    - plugins

- name: copy elasticsearchClient
  unarchive:
    src: "{{ elasticsearchXpack_tarball_name }}"
    dest: "/home/<USER>/server/elasticsearchClient"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
#    force: yes
    mode: 0755

# - name: copy elasticsearchclient
#   copy:
#     src: "server/elasticsearchClient"
#     dest: "/home/<USER>/server"
#     owner: "{{ bangcle_user }}"
#     group: "{{ bangcle_user }}"
#     force: yes
#     mode: 0755

- name: elasticsearchClient软连接到/home/<USER>/server/elasticsearchClient/elasticsearch
  file:
    src: "/home/<USER>/server/elasticsearchClient/elasticsearch-6.8.5"
    dest: "/home/<USER>/server/elasticsearchClient/elasticsearch"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False

- name: chmod elasticsearchclient bin
  command: "chmod -R 755 /home/<USER>/server/elasticsearchClient/elasticsearch/bin"

- name: copy mapper-murmur3
  copy:
    src: "{{ elasticsearch_mapper_murmur3_tarball_name }}"
    dest: "/home/<USER>/server/elasticsearchClient/plugins"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: 安装 mapper-murmur3
  become: yes
  become_user: "{{ bangcle_user }}"
  shell: "/home/<USER>/server/elasticsearchClient/elasticsearch/bin/elasticsearch-plugin install file:///home/<USER>/server/elasticsearchClient/plugins/mapper-murmur3-6.8.5.zip"
  environment:
    BASH_ENV: ~/.bash_profile
  ignore_errors: True

- name: 配置elasticsearchClient服务配置文件及脚本
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'elasticsearchClient.yml.j2' , value: 'elasticsearch/config/elasticsearch.yml' }
    - { key: 'jvm.options.j2' , value: 'elasticsearch/config/jvm.options' }
    - { key: 'log4j2.properties.j2' , value: 'elasticsearch/config/log4j2.properties' }
    - { key: 'bangcle_es_dog.sh.j2' , value: 'bin/bangcle_es_dog.sh' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'startup_elasticsearchClient.sh.j2' , value: 'bin/startup.sh' }

- name: 单机情况配置elasticsearchClient服务jvm.options配置文件，和内存有关
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'jvm.options.single.client.j2' , value: 'elasticsearch/config/jvm.options' }
  when:
    - groups['everisk-deploy'] | length == 1

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    path: "/home/<USER>/server/elasticsearchClient/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "elasticsearch/service_client_supervisor.sh.j2"
    dest: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: 在elasticsearchClient配置文件elasticsearch.yml增加xpack认证
  become_user: "{{ bangcle_user }}"
  lineinfile:
    path: /home/<USER>/server/elasticsearchClient/elasticsearch/config/elasticsearch.yml
    line: "{{ item }}"
  with_items:
    - 'xpack.security.enabled: true'
    - 'xpack.security.transport.ssl.enabled: true'
    - 'xpack.security.transport.ssl.key:  certs/bangcle.key'
    - 'xpack.security.transport.ssl.certificate: certs/bangcle.crt'
    - 'xpack.security.transport.ssl.certificate_authorities: certs/ca.crt'
    - 'xpack.security.transport.ssl.verification_mode: certificate'
  when: 
    - add_esxpack == true
    - ansible_architecture|lower == "x86_64"

- name: 在elasticsearchMaster配置文件elasticsearch.yml关闭机器学习
  become_user: "{{ bangcle_user }}"
  lineinfile:
    path: /home/<USER>/server/elasticsearchClient/elasticsearch/config/elasticsearch.yml
    line: "{{ item }}"
  with_items:
    - 'xpack.ml.enabled: false'
  when:
    - ansible_architecture|lower == "aarch64"

- name: 配置es的key文件
  template:
    src: "xpack/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/elasticsearch/config/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  with_items:
    - { key: 'bangcle.crt.j2' , value: 'certs/bangcle.crt' }
    - { key: 'bangcle.key.j2' , value: 'certs/bangcle.key' }
    - { key: 'ca.crt.j2' , value: 'certs/ca.crt' }
    - { key: 'ca.key.j2' , value: 'certs/ca.key' }
  when: add_esxpack == true 

- name: ready to notify
  shell: ls
  notify:
    - restart elasticsearchClient
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
