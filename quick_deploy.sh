#!/bin/bash

# 东莞银行风险管控系统快速部署脚本
# 部署用户: appuser
# 部署目录: /export

set -e

echo "=========================================="
echo "东莞银行风险管控系统快速部署脚本"
echo "部署用户: appuser"
echo "部署目录: /export"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 用户运行此脚本"
        exit 1
    fi
}

# 系统初始化
init_system() {
    log_info "开始系统初始化..."
    
    # 关闭防火墙和 SELinux
    systemctl stop firewalld 2>/dev/null || true
    systemctl disable firewalld 2>/dev/null || true
    setenforce 0 2>/dev/null || true
    sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config 2>/dev/null || true
    
    # 系统参数优化
    cat >> /etc/sysctl.conf << 'EOF'
fs.file-max=2048000
net.ipv4.tcp_tw_reuse=1
vm.swappiness=0
vm.max_map_count=262144
net.ipv4.ip_forward=1
net.ipv6.conf.all.forwarding=1
net.ipv4.tcp_fin_timeout=10
net.ipv4.tcp_tw_recycle=0
EOF
    
    sysctl -p
    
    # 用户资源限制
    cat >> /etc/security/limits.conf << 'EOF'
appuser soft nofile 65536
appuser hard nofile 65536
appuser soft memlock unlimited
appuser hard memlock unlimited
* soft nproc 65536
* - nofile 65536
EOF
    
    log_info "系统初始化完成"
}

# 创建用户和目录
create_user_and_dirs() {
    log_info "创建用户和目录结构..."
    
    # 创建用户组
    groupadd agent 2>/dev/null || true
    groupadd appuser 2>/dev/null || true

    # 创建用户
    if ! id appuser >/dev/null 2>&1; then
        useradd -m -s /bin/bash -G agent appuser
        echo "appuser:Dgbank@2024" | chpasswd
        log_info "用户 appuser 创建完成，密码: Dgbank@2024"
    else
        log_warn "用户 appuser 已存在"
    fi
    
    # 创建 /export 目录结构
    mkdir -p /export/{tools,app,bin,config,data,logs,server,source}
    chown -R appuser:appuser /export
    chmod -R 755 /export
    
    # 创建软链接
    ln -sf /export /home/<USER>/export 2>/dev/null || true
    
    log_info "用户和目录创建完成"
}

# 安装基础软件
install_basic_software() {
    log_info "安装基础软件..."
    
    # 更新 yum 源
    yum update -y
    
    # 安装基础工具
    yum install -y wget curl vim net-tools telnet nc

    # 安装 Redis
    yum install -y redis
    
    log_info "基础软件安装完成"
}

# 配置 JDK 环境
setup_jdk() {
    log_info "配置 JDK 环境..."
    
    # 检查 JDK 安装包
    if [ ! -f "jdk-8u*-linux-x64.tar.gz" ]; then
        log_warn "未找到 JDK 8 安装包，请手动下载并解压到 /export/tools/"
        log_warn "JDK 8: 解压到 /export/tools/jdk"
    fi

    # 配置环境变量
    cat > /home/<USER>/.bash_profile << 'EOF'
# User specific environment and startup programs
export JAVA_HOME=/export/tools/jdk
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar

# 加载用户自定义配置
if [ -f ~/.bashrc ]; then
    . ~/.bashrc
fi
EOF
    
    chown appuser:appuser /home/<USER>/.bash_profile
    
    log_info "JDK 环境配置完成"
}

# 配置数据库
setup_database() {
    log_info "配置 TDSQL 数据库连接..."

    # 创建数据库配置目录
    mkdir -p /export/config/database

    # 创建 TDSQL 配置文件
    cat > /export/config/database/tdsql.properties << 'EOF'
# TDSQL 数据库连接配置
db.driver=com.mysql.cj.jdbc.Driver
db.url=***********************************************************************************
db.username=everisk
db.password=everisk123
db.maxActive=20
db.initialSize=5
db.maxWait=60000
db.minIdle=5
db.timeBetweenEvictionRunsMillis=60000
db.minEvictableIdleTimeMillis=300000
db.validationQuery=SELECT 1
db.testWhileIdle=true
db.testOnBorrow=false
db.testOnReturn=false
EOF

    # 设置文件权限
    chown appuser:appuser /export/config/database/tdsql.properties
    chmod 600 /export/config/database/tdsql.properties
    
    # 配置 Redis
    sed -i "s/bind 127.0.0.1/bind 0.0.0.0/" /etc/redis.conf
    sed -i "s/# requirepass foobared/requirepass redis123/" /etc/redis.conf
    sed -i "s/# maxmemory <bytes>/maxmemory 2gb/" /etc/redis.conf
    
    # 启动 Redis
    systemctl start redis
    systemctl enable redis
    
    log_info "数据库配置完成"
}

# 创建管理脚本
create_management_scripts() {
    log_info "创建管理脚本..."
    
    # 复制详细管理脚本
    if [ -f "manage_services_verbose.sh" ]; then
        cp manage_services_verbose.sh /export/bin/
        chmod +x /export/bin/manage_services_verbose.sh
        chown appuser:appuser /export/bin/manage_services_verbose.sh
        log_info "已复制 manage_services_verbose.sh 到 /export/bin/"
    else
        log_warn "未找到 manage_services_verbose.sh 文件"
        # 创建简化版管理脚本
        cat > /export/bin/manage_services_verbose.sh << 'EOF'
#!/bin/bash
# 简化的服务管理脚本
EXPORT_DIR="/export"

case $1 in
    start)
        echo "启动所有服务..."
        # 这里需要根据实际部署的服务进行调整
        ;;
    stop)
        echo "停止所有服务..."
        # 这里需要根据实际部署的服务进行调整
        ;;
    status)
        echo "检查服务状态..."
        ps aux | grep java | grep appuser | grep -v grep
        ;;
    *)
        echo "Usage: $0 {start|stop|status}"
        ;;
esac
EOF
    fi
    
    # 创建检查脚本
    cat > /export/bin/check_services.sh << 'EOF'
#!/bin/bash
echo "=== 服务状态检查 ==="
echo "Redis: $(systemctl is-active redis)"
echo "Java 进程数: $(ps aux | grep java | grep appuser | grep -v grep | wc -l)"
echo "监听端口:"
netstat -tln | grep -E ":(2181|9092|6379|8080|8081|8082|8083|8084|8085) "
echo "TDSQL 配置文件: $([ -f /export/config/database/tdsql.properties ] && echo '存在' || echo '不存在')"
EOF
    
    chmod +x /export/bin/*.sh
    chown appuser:appuser /export/bin/*.sh
    
    log_info "管理脚本创建完成"
}

# 配置开机自启动
setup_autostart() {
    log_info "配置开机自启动..."

    # 首先创建专门的启动脚本
    cat > /export/bin/system_startup.sh << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动脚本

# 等待系统完全启动
sleep 30

# 设置日志文件
STARTUP_LOG="/export/logs/system_startup.log"
mkdir -p /export/logs

echo "=========================================" >> $STARTUP_LOG
echo "$(date) - 系统启动，开始启动服务..." >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

# 启动基础服务
echo "$(date) - 启动 Redis..." >> $STARTUP_LOG
systemctl start redis >> $STARTUP_LOG 2>&1

# 等待基础服务启动
sleep 10

# 启动 Zookeeper
echo "$(date) - 启动 Zookeeper..." >> $STARTUP_LOG
if [ -f "/export/server/zookeeper/bin/service.sh" ]; then
    su - appuser -c "/export/server/zookeeper/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 15
fi

# 启动 Kafka
echo "$(date) - 启动 Kafka..." >> $STARTUP_LOG
if [ -f "/export/server/kafka/bin/service.sh" ]; then
    su - appuser -c "/export/server/kafka/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 10
fi

# 启动应用服务
echo "$(date) - 启动应用服务..." >> $STARTUP_LOG
if [ -f "/export/bin/manage_services_verbose.sh" ]; then
    su - appuser -c "/export/bin/manage_services_verbose.sh start" >> $STARTUP_LOG 2>&1
fi

echo "$(date) - 所有服务启动完成" >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

exit 0
EOF

    # 设置启动脚本权限
    chmod +x /export/bin/system_startup.sh
    chown appuser:appuser /export/bin/system_startup.sh

    # 创建 systemd 服务文件
    cat > /etc/systemd/system/dongguanbank-risk.service << 'EOF'
[Unit]
Description=东莞银行风险管控系统
After=network.target multi-user.target
Wants=network.target

[Service]
Type=forking
User=root
ExecStart=/export/bin/start_all_services.sh
ExecStop=/export/bin/manage_services_verbose.sh stop
ExecReload=/export/bin/manage_services_verbose.sh restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=no
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载 systemd 配置
    systemctl daemon-reload

    # 然后在 rc.local 中通过 systemctl 启动服务
    cat > /etc/rc.d/rc.local << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 配置启动方式
STARTUP_MODE="systemd"  # 可选值：script, systemd

case "$STARTUP_MODE" in
    "script")
        # 直接调用启动脚本
        /export/bin/system_startup.sh &
        ;;
    "systemd")
        # 通过 systemd 服务启动（推荐）
        systemctl start dongguanbank-risk.service
        ;;
    *)
        # 默认使用脚本模式
        /export/bin/system_startup.sh &
        ;;
esac

exit 0
EOF

    # 设置执行权限
    chmod +x /etc/rc.d/rc.local

    # 启用 rc-local 服务
    systemctl enable rc-local 2>/dev/null || true

    log_info "开机自启动配置完成"
}

# 主函数
main() {
    log_info "开始部署东莞银行风险管控系统..."
    
    check_root
    init_system
    create_user_and_dirs
    install_basic_software
    setup_jdk
    setup_database
    create_management_scripts
    setup_autostart
    
    log_info "=========================================="
    log_info "基础环境部署完成！"
    log_info "=========================================="
    log_info "下一步操作："
    log_info "1. 上传 JDK 8 安装包到服务器并解压到 /export/tools/jdk"
    log_info "2. 配置 TDSQL 数据库连接信息 (/export/config/database/tdsql.properties)"
    log_info "3. 上传应用程序包到 /export/source/"
    log_info "4. 按照部署指南配置 Zookeeper 和 Kafka"
    log_info "5. 部署应用服务"
    log_info "=========================================="
    log_info "用户信息："
    log_info "用户名: appuser"
    log_info "密码: Dgbank@2024"
    log_info "部署目录: /export"
    log_info "=========================================="
}

# 执行主函数
main "$@"
