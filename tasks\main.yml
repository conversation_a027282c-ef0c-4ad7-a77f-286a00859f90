---
# tasks file for everisk-deploy
## update
# # 备份所有服务
# - import_tasks: backupAllServer.yml
#   tags: 
#     - never
#     - update
#     - backupAll

# 停止所有服务
- import_tasks: stopAllServer.yml
  tags: 
    - never
    - 4.8to4.9
    - stopAll

# 配置文件调整
- import_tasks: 48to49.yml
  tags: 
    - never
    - 4.8to4.9

# # checkCDH
# - import_tasks: checkCDH.yml
#   tags: 
#     - always
#     - update

# checkHadoopHA
- import_tasks: checkHadoopHA.yml
  tags:
    - install
    - update510
    - check_hadoop_ha
    - always

# editHostname
- import_tasks: editHostname.yml
  tags: 
    - install
    - edit_hosts
#  when:
#    - ansible_architecture|lower == "x86_64"
#    - cdh_tag == None

## server
- import_tasks: installZookeeper.yml
  tags: 
    - install
    - install_zookeeper
  when: 
    - inventory_hostname in groups['zookeeper'] 
#    - cdh_tag == None

- import_tasks: installHadoop.yml
  tags:
    - install
    - install_hadoop
    - install_hdfs
  when:
    - inventory_hostname in groups['namenode'] or inventory_hostname in groups['datanode']
    - hdfs_tag == True

- import_tasks: installKafka.yml
  tags: 
    - install
    - install_kafka
  when: 
    - inventory_hostname in groups['kafka']
#    - cdh_tag == None

- import_tasks: installHbase.yml
  tags: 
    - install
    - install_hbase
    - update_hbase
    - update510
  when: 
    - inventory_hostname in groups['hbase']
#    - cdh_tag == None

- import_tasks: updateDocker.yml
  tags: 
    - never
    - update_docker

- import_tasks: dockerEnv.yml
  tags: 
    - never
    - deploy_docker

- import_tasks: installNginx.yml
  tags: 
    - install
    - install_nginx
  when: inventory_hostname in groups['nginx']

- import_tasks: installCrash.yml
  tags: 
    - install
    - install_crash
  when: inventory_hostname in groups['crash']

- import_tasks: installPostgres.yml
  tags: 
    - install
    - install_postgres
  when: inventory_hostname in groups['postgres']

- import_tasks: installRedis.yml
  tags: 
    - install
    - install_redis
  when: inventory_hostname in groups['redis']

# - import_tasks: installRedisCluster.yml
#   tags: 
#     - install
#     - update
#     - install_redis_cluster
#   when: inventory_hostname in groups['redis']

# - import_tasks: createRedisCluster.yml
#   tags: 
#     - install
#     - update
#     - create_redis_cluster
#   when: inventory_hostname == groups['redis'][0]

- import_tasks: installElasticsearchClient.yml
  tags: 
    - install
    - install_elasticsearchClient
  when: inventory_hostname in groups['elasticsearchClient']

- import_tasks: installElasticsearchMaster.yml
  tags: 
    - install
    - install_elasticsearchMaster
  when: inventory_hostname in groups['elasticsearchMaster']

#- import_tasks: updateElasticSearchIK.yml
#  tags: 
#    - update_app
#    - update_elasticsearchIK
#  when: inventory_hostname in groups['elasticsearchMaster'] or inventory_hostname in groups['elasticsearchClient']

- import_tasks: installKibana.yml
  tags: 
    - install
    - install_kibana
  when: inventory_hostname in groups['kibana']

- import_tasks: installMinio.yml
  tags: 
    - install
    - install_minio
    - 4.8to4.9
  when: inventory_hostname in groups['minio']

#- import_tasks: installNebula.yml
#  tags: 
#    - install
#    - update
#    - install_nebula
#  when: inventory_hostname in groups['nebula']

# #####update
# - import_tasks: esAlias.yml
#   tags: 
#     - never
#     - update
#     - install_esAlias
#   when: inventory_hostname in groups['transfer']


# # updateCDH
# - import_tasks: updateCDH.yml
#   tags: 
#     - update
#     - update_cdh
#   when:
#     - cdh_tag != None

## app

### 部署init
- import_tasks: installInit.yml
  tags: 
    - install
    - update
    - install_app
    - update_app
    - install_init
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['init']


### 部署receiver
- import_tasks: installReceiver.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_receiver
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['receiver']

### 部署cleaner
- import_tasks: installCleaner.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_cleaner
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['cleaner']

### 部署transfer
- import_tasks: installTransfer.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_transfer
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['transfer']

### 部署threat
- import_tasks: installThreat.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_threat
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['threat']

### 部署threat-index
- import_tasks: installThreat-index.yml
  tags:  
    - never
    - install_threat-index
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['threat-index']

### 部署bbg-sdk-request-decrypt
- import_tasks: installBbg-sdk-request-decrypt.yml
  tags:
    - never
    - install_bbg-sdk-request-decrypt
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['bbg-sdk-request-decrypt']

### 部署webService
- import_tasks: installWeb-service.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_web-service
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['web-service']

### 部署webService前端
- import_tasks: installWeb-service-nginx.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_web-service
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['web-service']

### 部署analyzerDev
- import_tasks: installAnalyzer-dev.yml
  tags:   
    - install
    - update
    - install_app
    - update_app
    - install_analyzer-dev
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['analyzer-dev']

# ## 部署analyzer-relation
# - import_tasks: installAnalyzer-relation.yml
#   tags:  
#     - install
#     - update
#     - install_app
#     - update_app
#     - install_analyzer-relation
#   when: inventory_hostname in groups['analyzer-relation']

### 部署security-event
- import_tasks: installSecurity-event.yml
  tags:  
    - install
    - update
    - install_app
    - update_app
    - install_security-event
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['security-event']

### 部署chinalife-account-push
- import_tasks: installChinalife-account-push.yml
  tags:
    - never
    - install_chinalife-account-push
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['chinalife-account-push']


# ### 部署dataAggregater
# - import_tasks: installDataAggregater.yml
#   tags:  
#     - install
#     - update
#     - install_app
#     - update_app
#     - install_dataAggregater
#     - 48to489
#   when: inventory_hostname in groups['dataAggregater']

# ### 部署restfulService
# - import_tasks: installRestfulService.yml
#   tags:  
#     - install
#     - update
#     - install_app
#     - update_app
#     - install_restfulService
#     - 48to489
#   when: inventory_hostname in groups['restfulService']

# ### 部署hunter
# - import_tasks: installHunter.yml
#   tags:  
#     - install
#     - update
#     - install_app
#     - update_app
#     - install_hunter
#     - 48to489
#   when: inventory_hostname in groups['hunter']

# ### 部署devMark
# - import_tasks: installDev-mark.yml
#   tags:  
#     - install
#     - update
#     - install_app
#     - update_app
#     - install_dev-mark
#     - 48to489
#   when: inventory_hostname in groups['dev-mark']

### 部署appSender
- import_tasks: installApp-sender.yml
  tags:  
    - never
    - install
#    - update
    - install_app-sender
    - 4.8to4.9
    - update510
  when: inventory_hostname in groups['app-sender']

### 部署app应用升级模块
- import_tasks: installUpgrade-service.yml
  tags:  
    - install
    - install_upgrade-service
    - 4.8to4.9
  when: 
    - inventory_hostname in groups['upgrade-service']
#    - ansible_architecture|lower == "x86_64"

- import_tasks: installUpgrade-web.yml
  tags:  
    - install
    - install_upgrade-web
    - 4.8to4.9
  when: 
    - inventory_hostname in groups['upgrade-service']
#    - ansible_architecture|lower == "x86_64"

# ### 调整489的supervisor的配置文件
# - import_tasks: 48to489.yml
#   tags: 
#     - never
#     - 48to489

# ## ops
# ### cesi
# - import_tasks: installCesi.yml
#   tags:
#     - install
#     - install_cesi
#   when: inventory_hostname in groups['cesi'] and supervisord_used == true

# ### supervisord
# - import_tasks: installSupervisor.yml
#   tags:
#     - install
#     - install_supervisor
#   when: inventory_hostname in groups[(role_name)] and supervisord_used == true

### monitor
- import_tasks: installMonitor.yml
  tags:  
    - never
    - install
    - install_monitor
  when:
    - ansible_architecture|lower == "x86_64"

### elk
- import_tasks: installELK.yml
  tags:  
    - never
    - install
    - install_ELK
    - 4.8to4.9
  when: inventory_hostname in groups['ELK']

### filebeat
- import_tasks: installFilebeat.yml
  tags:  
    - never
    - install
    - install_filebeat
    - 4.8to4.9

### portainer
#- import_tasks: installPortainer.yml
#  tags: 
#    - install
#    - install_portainer
#    - 4.8to4.9
#  when:
#    - ansible_architecture|lower == "x86_64"

### 数据修复
# - import_tasks: esOptimization.yml
#   tags: install_esOptimization
#   when: inventory_hostname in groups['transfer']

# - import_tasks: installToolsEs.yml
#   tags:  
#     - never
#     - update
#     - install_toolsEs
#   when: inventory_hostname in groups['transfer']

### api_autotest
- import_tasks: installApiAutotest.yml
  tags:  
    - never
    - install
    - install_api_autotest
  when: 
    - inventory_hostname in groups['transfer']
    - ansible_architecture|lower == "x86_64"

# ### 清理4.8.9无用服务
# - import_tasks: remove489Server.yml
#   tags:
#     - never
#     - 48to489

# ### 清理用户
# - import_tasks: dropUser.yml
#   tags:
#     - never
#     - drop_user

### 修复漏洞
- import_tasks: updateIptables.yml
  tags:
    - never
    - update_iptables

- import_tasks: checkDistribution.yml
  tags:
    - never
    - check_distribution

### 清理用户
- import_tasks: unInstallServer.yml
  tags:
    - never
    - cleanup

# 重启supervisor
# - import_tasks: restartSupervisorctl.yml
#   tags:
#     - always

# 回退
- import_tasks: rollbackInit.yml
  vars:
    rollback_app: init
  tags: 
    - never
    - rollback
    - rollback_init
  when: inventory_hostname in groups['init']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: receiver
  tags: 
    - never
    - rollback
    - rollback_receiver
  when: inventory_hostname in groups['receiver']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: cleaner
  tags: 
    - never
    - rollback
    - rollback_cleaner
  when: inventory_hostname in groups['cleaner']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: transfer
  tags: 
    - never
    - rollback
    - rollback_transfer
  when: inventory_hostname in groups['transfer']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: threat
  tags: 
    - never
    - rollback
    - rollback_threat
  when: inventory_hostname in groups['threat']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: security-event
  tags: 
    - never
    - rollback
    - rollback_security-event
  when: inventory_hostname in groups['security-event']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: analyzer-dev
  tags: 
    - never
    - rollback
    - rollback_analyzer-dev
  when: inventory_hostname in groups['analyzer-dev']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: web-service
  tags: 
    - never
    - rollback
    - rollback_web-service
  when: inventory_hostname in groups['web-service']

- import_tasks: rollbackServer.yml
  vars:
    rollback_server: web-service-nginx
  tags: 
    - never
    - rollback
    - rollback_web-service
    - rollback_web-service-nginx
  when: inventory_hostname in groups['web-service']

- import_tasks: fetchInit.yml
  tags:
    - never
    - fetch
  when: inventory_hostname in groups['init']

- import_tasks: updateInit.yml
  tags:
    - never
    - update_init
  when: inventory_hostname in groups['init']

- import_tasks: ElasticsearchSecurity.yml
  tags:
    - install
    - ElasticsearchSecurity
    - add_es_user
  when: 
    - ansible_architecture|lower == "x86_64"

- import_tasks: setKafkaTopic.yml
  tags:
    - set_kafka_topic
