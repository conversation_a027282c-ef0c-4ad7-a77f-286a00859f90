---

- name: check server脚本
  stat:
    path: "/home/<USER>/bin/service_server_all.sh"
  register: server_bin_exist
  ignore_errors: True

- name: 停止所有server服务
  become_user: "{{ bangcle_user }}"
  command: "bash /home/<USER>/bin/service_server_all.sh stop && sleep 5"
  ignore_errors: True
  when: server_bin_exist.stat.exists  == True

- name: 输出debug
  debug:
    msg: "{{ server_bin_exist }}"

- name: check app脚本
  stat:
    path: "/home/<USER>/bin/service_app_all.sh"
  register: app_bin_exist
  ignore_errors: True

- name: 停止所有app服务
  become_user: "{{ bangcle_user }}"
  command: "bash /home/<USER>/bin/service_app_all.sh stop && sleep 5"
  ignore_errors: True
  when: app_bin_exist.stat.exists  == True

- name: 输出debug
  debug:
    msg: "{{ app_bin_exist }}"

- name: check ops脚本
  stat:
    path: "/home/<USER>/bin/service_ops_all.sh"
  register: ops_bin_exist
  ignore_errors: True

- name: 停止所有ops服务
  become_user: "{{ bangcle_user }}"
  command: "bash /home/<USER>/bin/service_ops_all.sh stop && sleep 5"
  ignore_errors: True
  when: ops_bin_exist.stat.exists  == True

- name: 输出debug
  debug:
    msg: "{{ ops_bin_exist }}"

- name: check docker服务
  stat:
    path: "/etc/systemd/system/docker.service"
  register: docker_exist
  ignore_errors: True

- name: 停止docker服务
  command: "systemctl stop docker"
  ignore_errors: True
  when: docker_exist.stat.exists == True

- name: 删除docker服务
  file:
    path: "/etc/systemd/system/docker.service"
    # force: yes
    state: absent
  ignore_errors: True
  when: docker_exist.stat.exists  == True

- name: 删除docker.pid
  file:
    path: "/run/docker.pid"
    state: absent
  ignore_errors: True
  when: docker_exist.stat.exists  == True

- name: wait for 30 seconds
  pause:
    seconds: 30

- name: delete mount file system if possible
  shell: "df|grep overlay2|awk '{print $NF}'|xargs umount"
  ignore_errors: True

- name: 停止所有服务
  command: "pkill -u {{ bangcle_user }} -9 "
  ignore_errors: True

- name: 删除所有的服务
  user:
    name: "{{ bangcle_user }}"
    state: absent
    remove: yes
