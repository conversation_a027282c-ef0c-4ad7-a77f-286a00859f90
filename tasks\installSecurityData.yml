---
#- tags: install_securityData

- import_tasks: createUser.yml
- import_tasks: backupApp.yml
  vars: 
    backup_server: securityData

- name: 安装securityData包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ securityData_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/securityData/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/securityData/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/securityData/application-zk.properties.j2"
    flat: yes
  when: check_application_zk.stat.exists == True

- name: 配置securityData的application-zk.properties
  template:
    src: securityData/application-zk.properties.j2
    dest: /home/<USER>/app/securityData/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_zk.stat.exists == True

- name: check  application-remote .properties
  stat: 
    path: "/home/<USER>/app/securityData/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/securityData/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/securityData/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置securityData的application-remote.properties
  template:
    src: securityData/application-remote.properties.j2
    dest: /home/<USER>/app/securityData/config/application-remote.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_remote.stat.exists == True

- name: 配置securityData的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/securityData/config/hbase-site.xml

- name: configure startup.sh
  template:
    src: "securityData/startup.sh.j2"
    dest: "/home/<USER>/app/securityData/bin/startup.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: check  /tmp/4602/hdfs-site.xml
  stat: 
    path: "/tmp/4602/hdfs-site.xml"
  register: check_cdh
  connection: local
  ignore_errors: True

- name: CDH版本配置文件
  copy:
    src: "/tmp/4602/{{ item }}"
    dest: "/home/<USER>/app/securityData/config/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - "core-site.xml"
    - "hbase-site.xml"
    - "hdfs-site.xml"
  when: check_cdh.stat.exists == True

- name: 调整securityData的zk地址
  lineinfile: 
    dest: "/home/<USER>/app/securityData/config/application-zk.properties"
    regexp: "^everisk.config.zk.host="
    line: "everisk.config.zk.host={{ zookeeper_addr }}"
  when: zookeeper_addr != None

- name: 调整securityData的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/securityData/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/securityData/bin/service.sh"
    path: "/home/<USER>/app/securityData/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/securityData/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "securityData/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/securityData/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart securityData
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
