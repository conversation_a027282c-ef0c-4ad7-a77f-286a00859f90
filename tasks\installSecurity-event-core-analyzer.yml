---
#- tags: install_security-event-core-analyzer

- import_tasks: createUser.yml

- name: 安装security-event-core-analyzer包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ coreAnalyzer_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/security-event-core-analyzer/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/security-event-core-analyzer/application-zk.properties.j2"
    flat: yes

- name: 配置security-event-core-analyzer的application-zk.properties
  template:
    src: security-event-core-analyzer/application-zk.properties.j2
    dest: /home/<USER>/app/security-event-core-analyzer/config/application-zk.properties

- name: 配置security-event-core-analyzer的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/security-event-core-analyzer/config/hbase-site.xml

- name: 调整security-event-core-analyzer的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/security-event-core-analyzer/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"
  notify:
    - restart security-event-core-analyzer

- name: 提前重启服务
  meta: flush_handlers
