---
# tags: deploy_nginx
- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 安装nginx包
  copy:
    src: "server/nginx"
    dest: "/home/<USER>/server/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/nginx/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - config/conf.d
    - images
    - log
    - data
    - tmp

- name: restart docker
  command: "systemctl restart docker"

- name: load nginx image
  command: "docker load -i /home/<USER>/server/nginx/images/{{ ansible_architecture|lower }}/nginx.tar"

- name: restart docker
  command: "systemctl restart docker"

- name: 创建部署所需要的日志
  file:
    path: "/home/<USER>/server/nginx/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    # recurse: yes
    state: touch
  with_items:
    - log/error.log

- import_tasks: checkIPv6.yml

- name: 配置nginx服务配置文件及脚本，当支持ipv6时
  template:
    src: "nginx/{{ item.key }}"
    dest: "/home/<USER>/server/nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'down.conf.j2' , value: 'config/conf.d/down.conf' }
    - { key: 'nginx.conf.j2' , value: 'config/nginx.conf' }
    - { key: 'status.conf.j2' , value: 'config/conf.d/status.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "x86_64" and supports_ipv6

- name: 配置nginx服务配置文件及脚本，当不支持ipv6时
  template:
    src: "nginx/{{ item.key }}"
    dest: "/home/<USER>/server/nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'down_ipv4.conf.j2' , value: 'config/conf.d/down.conf' }
    - { key: 'nginx.conf.j2' , value: 'config/nginx.conf' }
    - { key: 'status.conf.j2' , value: 'config/conf.d/status.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "x86_64" and not supports_ipv6

- name: 配置nginx服务配置文件及脚本，当支持ipv6时
  template:
    src: "nginx/{{ item.key }}"
    dest: "/home/<USER>/server/nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'down_aarch64.conf.j2' , value: 'config/conf.d/down.conf' }
    - { key: 'nginx.conf.j2' , value: 'config/nginx.conf' }
    - { key: 'status.conf.j2' , value: 'config/conf.d/status.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "aarch64" and supports_ipv6

- name: 配置nginx服务配置文件及脚本，当不支持ipv6时
  template:
    src: "nginx/{{ item.key }}"
    dest: "/home/<USER>/server/nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'down_aarch64_ipv4.conf.j2' , value: 'config/conf.d/down.conf' }
    - { key: 'nginx.conf.j2' , value: 'config/nginx.conf' }
    - { key: 'status.conf.j2' , value: 'config/conf.d/status.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "aarch64" and not supports_ipv6

- name: list
  shell: ls
  notify:
    - restart nginx
- name: 提前重启服务
  meta: flush_handlers
