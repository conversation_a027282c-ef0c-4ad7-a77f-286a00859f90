---
## 停止cesi
- name: check cesi
  stat: 
    path: "/home/<USER>/ops/cesi"
  register: user_cesi_status
  ignore_errors: True
  
## 停止cesi
- name: 停止cesi
  command: "su - {{ bangcle_user }} -c \" ./ops/cesi/bin/service.sh stop\""
  when: 
    - user_cesi_status == True
  ignore_errors: True

## 停止supervisor
- name: check  supervisor
  stat: 
    path: "/home/<USER>/ops/supervisor"
  register: user_supervisor_status
  ignore_errors: True
  
## 停止supervisor
- name: 停止supervisor
  command: "su - {{ bangcle_user }} -c \" ./ops/supervisor/bin/service.sh stop\""
  when: 
    - user_supervisor_status == True
  ignore_errors: True

## 停止server
- name: 停止server
  command: "su - {{ bangcle_user }} -c \" ./bin/service_server_all.sh stop\""
  ignore_errors: True
  
## 停止app
- name: 停止app
  command: "su - {{ bangcle_user }} -c \" ./bin/service_app_all.sh stop\""
  ignore_errors: True

- block:
  - name: 停止elk
    command: "su - {{ bangcle_user }} -c \" ./ops/elk/bin/service.sh stop\""
    ignore_errors: True

  - name: 停止filebeat
    command: "su - {{ bangcle_user }} -c \" ./ops/filebeat/bin/service.sh stop\""
    ignore_errors: True
  
  - name: 删除elk
    file:
      path: /home/<USER>/ops/elk
      state: absent
    ignore_errors: True

  - name: 删除filebeat
    file:
      path: /home/<USER>/ops/filebeat
      state: absent
    ignore_errors: True
  when: inventory_hostname in groups['ELK']

- block:
  - name: 停止nebula
    command: "su - {{ bangcle_user }} -c \"ps -ef | grep 'nebula'|grep -v grep | awk '{print $2}'|xargs kill -9 \""
    ignore_errors: True

  - name: 删除nebula
    file:
      path: /home/<USER>/server/nebula
      state: absent
    ignore_errors: True
  when: inventory_hostname in groups['nebula']