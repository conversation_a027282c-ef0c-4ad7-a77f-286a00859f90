---
#- tags: install_securityCore

- import_tasks: createUser.yml
- import_tasks: backupApp.yml
  vars: 
    backup_server: securityCore

- name: 安装securityCore包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ securityCore_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/securityCore/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/securityCore/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/securityCore/application-zk.properties.j2"
    flat: yes
  when: check_application_zk.stat.exists == True

- name: 配置securityCore的application-zk.properties
  template:
    src: securityCore/application-zk.properties.j2
    dest: /home/<USER>/app/securityCore/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_zk.stat.exists == True

- name: check  application-remote.properties
  stat: 
    path: "/home/<USER>/app/securityCore/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/securityCore/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/securityCore/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置securityCore的application-remote.properties
  template:
    src: securityCore/application-remote.properties.j2
    dest: /home/<USER>/app/securityCore/config/application-remote.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_remote.stat.exists == True

- name: 配置securityCore的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/securityCore/config/hbase-site.xml

- name: configure startup.sh
  template:
    src: "securityCore/startup.sh.j2"
    dest: "/home/<USER>/app/securityCore/bin/startup.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: check  /tmp/4602/hdfs-site.xml
  stat: 
    path: "/tmp/4602/hdfs-site.xml"
  register: check_cdh
  connection: local
  ignore_errors: True

- name: CDH版本配置文件
  copy:
    src: "/tmp/4602/{{ item }}"
    dest: "/home/<USER>/app/securityCore/config/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - "core-site.xml"
    - "hbase-site.xml"
    - "hdfs-site.xml"
  when: check_cdh.stat.exists == True

- name: 调整securityCore的zk地址
  lineinfile: 
    dest: "/home/<USER>/app/securityCore/config/application-zk.properties"
    regexp: "^everisk.config.zk.host="
    line: "everisk.config.zk.host={{ zookeeper_addr }}"
  when: zookeeper_addr != None

- name: 调整securityCore的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/securityCore/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/securityCore/bin/service.sh"
    path: "/home/<USER>/app/securityCore/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/securityCore/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "securityCore/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/securityCore/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart securityCore
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
