---

##es-client
- name: check elasticsearchClient是否安装
  stat: 
    path: "/home/<USER>/server/elasticsearchClient/"
  register: check_es_client
  ignore_errors: True

- name: 配置elasticsearchClient服务配置文件及脚本
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup_elasticsearchClient.sh.j2' , value: 'bin/startup.sh' }
  when:
    - supervisord_used == true
    - check_es_client.stat.exists == True

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    path: "/home/<USER>/server/elasticsearchClient/bin/preservice.sh"
    state: hard
    force: yes
  when: 
    - supervisord_used == true
    - check_es_client.stat.exists == True
  ignore_errors: True

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_es_client.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "elasticsearch/service_client_supervisor.sh.j2"
    dest: "/home/<USER>/server/elasticsearchClient/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_es_client.stat.exists == True
  ignore_errors: True


##es-master

- name: check elasticsearchClient是否安装
  stat: 
    path: "/home/<USER>/server/elasticsearchMaster/"
  register: check_es_master
  ignore_errors: True

- name: 配置elasticsearchMaster服务配置文件及脚本
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchMaster/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup_elasticsearchMaster.sh.j2' , value: 'bin/startup.sh' }
  when:
    - supervisord_used == true
    - check_es_master.stat.exists == True

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/elasticsearchMaster/bin/service.sh"
    path: "/home/<USER>/server/elasticsearchMaster/bin/preservice.sh"
    state: hard
    force: yes
  when:
    - supervisord_used == true
    - check_es_master.stat.exists == True
  ignore_errors: True

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/elasticsearchMaster/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_es_master.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "elasticsearch/service_master_supervisor.sh.j2"
    dest: "/home/<USER>/server/elasticsearchMaster/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_es_master.stat.exists == True
  ignore_errors: True

### hbase

- name: check hbase是否安装
  stat: 
    path: "/home/<USER>/server/hbase/"
  register: check_hbase_master
  ignore_errors: True

- name: 配置hbase服务配置文件及脚本
  template:
    src: "hbase/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup_master.sh.j2' , value: 'hbase/bin/startup_master.sh' }
    - { key: 'startup_regionserver.sh.j2' , value: 'hbase/bin/startup_regionserver.sh' }
  when:
    - supervisord_used == true
    - check_hbase_master.stat.exists == True


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/hbase/bin/service.sh"
    path: "/home/<USER>/server/hbase/bin/preservice.sh"
    state: hard
    force: yes
  when:
    - supervisord_used == true
    - check_hbase_master.stat.exists == True
  ignore_errors: True
  

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/hbase/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_hbase_master.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "hbase/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/hbase/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_hbase_master.stat.exists == True
  ignore_errors: True


### kafka

- name: check kafka是否安装
  stat: 
    path: "/home/<USER>/server/kafka/"
  register: check_kafka_master
  ignore_errors: True

- name: 配置kafka的配置文件及脚本
  template:
    src: "kafka/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup.sh.j2' , value: 'kafka/bin/startup.sh' }
  when:
    - supervisord_used == true
    - check_kafka_master.stat.exists == True

- name: apply kafka-run-class file, only for aarch64
  copy:
    src: "server/kafka/kafka-run-class.sh"
    dest: "/home/<USER>/server/kafka/kafka/bin/kafka-run-class.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: 
    - ansible_architecture|lower == "aarch64"
    - supervisord_used == true
    - check_kafka_master.stat.exists == True
  ignore_errors: True


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/kafka/bin/service.sh"
    path: "/home/<USER>/server/kafka/bin/preservice.sh"
    state: hard
    force: yes
  when:
    - supervisord_used == true
    - check_kafka_master.stat.exists == True
  ignore_errors: True


- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/kafka/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_kafka_master.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "kafka/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/kafka/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_kafka_master.stat.exists == True
  ignore_errors: True


### kibana

- name: check kibana是否安装
  stat: 
    path: "/home/<USER>/server/kibana/"
  register: check_kibana_master
  ignore_errors: True

- name: 配置kibana服务配置文件及脚本
  template:
    src: "kibana/{{ item.key }}"
    dest: "/home/<USER>/server/kibana/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when:
    - supervisord_used == true
    - check_kibana_master.stat.exists == True
  ignore_errors: True

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/kibana/bin/service.sh"
    path: "/home/<USER>/server/kibana/bin/preservice.sh"
    state: hard
    force: yes
  when:
    - supervisord_used == true
    - check_kibana_master.stat.exists == True
  ignore_errors: True

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/kibana/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_kibana_master.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "kibana/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/kibana/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_kibana_master.stat.exists == True
  ignore_errors: True


### zookeeper

- name: check zookeeper是否安装
  stat: 
    path: "/home/<USER>/server/zookeeper/"
  register: check_zookeeper_master
  ignore_errors: True

- name: 配置zookeeper的配置文件及脚本
  template:
    src: "zookeeper/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'startup.sh.j2' , value: 'zookeeper/bin/startup.sh' }
  when:
    - supervisord_used == true
    - check_zookeeper_master.stat.exists == True


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/zookeeper/bin/service.sh"
    path: "/home/<USER>/server/zookeeper/bin/preservice.sh"
    state: hard
    force: yes
  when:
    - supervisord_used == true
    - check_zookeeper_master.stat.exists == True
  ignore_errors: True

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/zookeeper/bin/service.sh"
    state: absent
  when:
    - supervisord_used == true
    - check_zookeeper_master.stat.exists == True
  ignore_errors: True

- name: configure service.sh in supervisor mode
  template:
    src: "zookeeper/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/zookeeper/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when:
    - supervisord_used == true
    - check_zookeeper_master.stat.exists == True
  ignore_errors: True
