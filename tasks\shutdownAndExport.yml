---
#- tags: shutdown_export

# 导出4602版本postgreps数据
- name: 4602版本postgreps数据导出
  shell: docker exec -it bb_pg pg_dump -U bangcle_pg bangcle_everisk_v4 > /bb_home/bb_pg/postgres4602.sql
  when: inventory_hostname in groups['postgres']

# # 关闭4602版elasticsearch服务
# - name: 关闭elasticsearch服务
#   become_user: bb_es
#   shell: /bb_home/bb_es/service stop
#   ignore_errors: yes
#   when: inventory_hostname in groups['elasticsearchMaster']

# # 关闭4602版kibana服务
# - name: 关闭kibana服务
#   become_user: bb_kibana
#   shell: /bb_home/bb_kibana/service stop
#   ignore_errors: yes
#   when: inventory_hostname in groups['kibana']

# 关闭4602版nginx服务


# 关闭4602版posgres服务


# 关闭4602版redis服务


# 关闭4602版crash服务


# 关闭4602版docker服务，并卸载docker组件



