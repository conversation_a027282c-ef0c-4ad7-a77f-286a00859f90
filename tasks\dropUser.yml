---

- name:  删除4.6用户
  user:
    name: "{{ item }}"
    state: absent
    remove: true
  ignore_errors: True
  with_items:
    - bb_nginx
    - bb_redis
    - bb_crash
    - bb_pg
    - bb_docker
    - bb_es
    - bb_hbase
    - bb_kafka
    - bb_zk
    - bb_alertserver
    - bb_analyzerdev
    - bb_analyzerevent
    - bb_appsender
    - bb_cleaner
    - bb_hunter
    - bb_init
    - bb_kibana
    - bb_receiver
    - bb_threat
    - bb_transfer
    - bb_webservice
    - bb_monitor
    - bb_restful

- name:  删除4.3用户
  user:
    name: "{{ item }}"
    state: absent
    remove: true
  ignore_errors: True
  with_items:
    - bangcle_nginx
    - bangcle_redis
    - bangcle_crash
    - bangcle_pg
    - bangcle_es
    - bangcle_alertserver
    - bangcle_analyzerdev
    - bangcle_analyzerevent
    - bangcle_appsender
    - bangcle_cleaner
    - bangcle_hunter
    - bangcle_init
    - bangcle_kibana
    - bangcle_receiver
    - bangcle_threat
    - bangcle_transfer
    - bangcle_webservice
    - bangcle_monitor
    - bnagcle_anservice
    - bangcle_devstatus
    - bangcle_alarm
    - bangcle_dataservice
    - bangcle_map
    - bangcle_restful

- name: 清除/bb_home
  file:
    path: /bb_home
    force: yes
    state: absent
  ignore_errors: True

- name: 清除4.3版本软连接
  file:
    path: "/home/<USER>"
    force: yes
    state: absent
  with_items:
    - bangcle_nginx
    - bangcle_redis
    - bangcle_crash
    - bangcle_pg
    - bangcle_es
    - bangcle_alertserver
    - bangcle_analyzerdev
    - bangcle_analyzerevent
    - bangcle_appsender
    - bangcle_cleaner
    - bangcle_hunter
    - bangcle_init
    - bangcle_kibana
    - bangcle_receiver
    - bangcle_threat
    - bangcle_transfer
    - bangcle_webservice
    - bangcle_monitor
    - bnagcle_anservice
    - bangcle_devstatus
    - bangcle_alarm
    - bangcle_dataservice
    - bangcle_map
    - bangcle_restful
  ignore_errors: True

- name: 清除开机自启动
  lineinfile:
    dest:  /etc/rc.local
    state: absent
    regexp: "{{ item }}"
  with_items:
    - bangcle_nginx
    - bangcle_redis
    - bangcle_crash
    - bangcle_pg
    - bangcle_es
    - bangcle_alertserver
    - bangcle_analyzerdev
    - bangcle_analyzerevent
    - bangcle_appsender
    - bangcle_cleaner
    - bangcle_hunter
    - bangcle_init
    - bangcle_kibana
    - bangcle_receiver
    - bangcle_threat
    - bangcle_transfer
    - bangcle_webservice
    - bangcle_monitor
    - bnagcle_anservice
    - bangcle_devstatus
    - bangcle_alarm
    - bangcle_dataservice
    - bangcle_map
    - bangcle_restful
    - hdfs
    - bb_nginx
    - bb_redis
    - bb_crash
    - bb_pg
    - bb_docker
    - bb_es
    - bb_hbase
    - bb_kafka
    - bb_zk
    - bb_alertserver
    - bb_analyzerdev
    - bb_analyzerevent
    - bb_appsender
    - bb_cleaner
    - bb_hunter
    - bb_init
    - bb_kibana
    - bb_receiver
    - bb_threat
    - bb_transfer
    - bb_webservice
    - bb_monitor
    - bb_restful
  ignore_errors: True