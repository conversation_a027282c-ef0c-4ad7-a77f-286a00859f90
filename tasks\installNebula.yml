---
# tags: deploy_nebula

- import_tasks: createUser.yml

- name: 创建nebula的bin目录
  file:
    path: "/home/<USER>/server/nebula"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory

- name: 安装nebula
  unarchive:
    src: "{{ nebula_tarball_name }}"
    dest: "/home/<USER>/server/nebula"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    creates: nebula
    force: yes
    mode: 0755

- name: 创建nebula的bin目录
  file:
    path: "/home/<USER>/server/nebula/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - logs
    - data/meta
    - data/storage

- name: 配置nebula服务配置文件
  template:
    src: "nebula/{{ item.key }}"
    dest: "/home/<USER>/server/nebula/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'nebula-graphd.conf.j2' , value: 'nebula/etc/nebula-graphd.conf' }
    - { key: 'nebula-metad.conf.j2' , value: 'nebula/etc/nebula-metad.conf' }
    - { key: 'nebula-storaged.conf.j2' , value: 'nebula/etc/nebula-storaged.conf' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'bangcle_nebula_dog.sh.j2' , value: 'bin/bangcle_nebula_dog.sh' }
    - { key: 'startup_meta.sh.j2' , value: 'bin/startup_meta.sh' }
    - { key: 'startup_graph.sh.j2' , value: 'bin/startup_graph.sh' }
    - { key: 'startup_storage.sh.j2' , value: 'bin/startup_storage.sh' }

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/nebula/bin/service.sh"
    path: "/home/<USER>/server/nebula/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/nebula/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "nebula/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/nebula/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - start nebula
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
