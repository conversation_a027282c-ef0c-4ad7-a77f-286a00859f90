---
#- tags: backupALLServer
- import_tasks: createUser.yml

- name: 创建备份目录
  file:
    path: "../backups/4602/{{ item }}"
    force: yes
    mode: 0777
    recurse: yes
    state: directory
  with_items: 
    "{{ groups['all'] }}"
  connection: local

#- name: copy rpm包
#  copy:
#    src: "{{ item }}"
#    dest: /tmp/rpms/
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    mode: 0644
#  with_fileglob:
#    - "tools/rpms/*.rpm"
#  register: rpms_copied

#- name: 输出rpms
#  debug:
#    msg: "{{ rpms_copied }}"

#- name: local RPMs not found
#  fail:
#    msg: "RPMs not found in ../files/"
#  when: rpms_copied.results|length == 0 and rpms_copied.skipped and rpms_copied.skipped_reason.find('No items') != -1

#- name: rpm包数据化
#  set_fact:
#    rpm_list: "{{ rpms_copied.results | map(attribute='dest') | list}}"

#- name: install RPMs
#  yum:
#    name: "{{rpm_list}}"

################ 4.6 app
- name: check  /bb_home
  stat: 
    path: "/bb_home/"
  register: check_bangcle_user
  ignore_errors: True

- name: 获取bb用户列表
  shell:  ls  -ld /bb_home/bb_*/*|grep "^d"|awk '{print $NF}'|egrep "alertServer|analyzerDev|analyzerEvent|appSender|cleaner|hunter|init|receiver|threat|transfer|alertClient|webService"
  register: bb_user_list
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

- name: 输出bb用户列表
  debug:
    msg: "{{ bb_user_list }}"

- name: 备份receiver-4.6
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/"
    rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  with_items:
    - "{{ bb_user_list.stdout_lines }}"
  when: check_bangcle_user.stat.exists == True

################# 4.3 app

- name: 获取bangcle用户列表
  shell:  ls  -ld /home/<USER>/*|grep "^d"|awk '{print $NF}'|egrep "alertServer|analyzerDev|analyzerEvent|appSender|cleaner|hunter|init|receiver|threat|transfer|alertClient|webService|anservice|devstatus|alarm"
  register: bb_user_list
  ignore_errors: True

- name: 输出bangcle用户列表
  debug:
    msg: "{{ bb_user_list }}"

- name: 备份所有app服务
  synchronize:
    mode: pull
    src: "{{ item }}"
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/"
    rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  # with_items:
  #   - "bb_receiver/receiver1"
  #   - "bb_cleaner/cleaner1"
  with_items:
    - "{{ bb_user_list.stdout_lines }}"
  when: bb_user_list.failed == False
############

####备份pg
##### 4.6
- name: check  /bb_home/bb_pg
  stat: 
    path: "/bb_home/bb_pg"
  register: check_bangcle_user
  ignore_errors: True

- name: 备份postgresql-4.6
  synchronize:
    mode: pull
    src: /bb_home/bb_pg/data/postgres/9.6/data
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/postgres/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

###### 4.3
- name: check  /home/<USER>
  stat: 
    path: "/home/<USER>"
  register: check_bangcle_user
  ignore_errors: True

- name: 备份postgresql-4.6
  synchronize:
    mode: pull
    src: /home/<USER>/postgres/9.6/data
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/postgres/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

##### 4.6 zookeeper
- name: check  /bb_home/bb_zk
  stat: 
    path: "/bb_home/bb_zk"
  register: check_bangcle_user
  ignore_errors: True

- name: 备份zookeeper-4.6
  synchronize:
    mode: pull
    src: /bb_home/bb_zk/*
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/zookeeper/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

- name: 备份zookeeper-4.6
  synchronize:
    mode: pull
    src: /bb_home/bb_zk/zk_data
    dest: "../backups/4602/zookeeper/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    delete: yes
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

##### 4.6 hbase
- name: check  /bb_home/bb_hbase
  stat: 
    path: "/bb_home/bb_hbase"
  register: check_bangcle_user
  ignore_errors: True

- name: 备份hbase-4.6
  synchronize:
    mode: pull
    src: /bb_home/bb_hbase/*
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/hbase/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

# - name: 清除hbase-4.6的备份目录
#   command: "rm -rf /bb_home/bb_hbase/backups 2>/dev/null"

# - name: 备份hbase-4.6
#   shell:  "su - bb_hbase -c \"./hbase/bin/hbase org.apache.hadoop.hbase.mapreduce.Export {{ item }} file:///bb_home/bb_hbase/backups/{{ item }} \" "
#   with_items:
#     - bangcle_cleaner_apk_install                                                                                                                                          
#     - bangcle_cleaner_cache                                                                                                                                                
#     - bangcle_cleaner_devinfo_cache                                                                                                                                        
#     - bangcle_cleaner_self_process_app_counter_cache                                                                                                                       
#     - bangcle_cleaner_self_process_app_value_cache                                                                                                                         
#     - bangcle_cleaner_self_process_so_counter_cache                                                                                                                        
#     - bangcle_cleaner_self_process_so_value_cache                                                                                                                          
#     - bangcle_cleaner_start_cache                                                                                                                                          
#     - bangcle_config_sync                                                                                                                                                  
#     - bangcle_dev_location_status                                                                                                                                          
#     - bangcle_dev_status                                                                                                                                                   
#     - bangcle_safe_event_threat_list                                                                                                                                       
#     - bangcle_sparkoffset                                                                                                                                                  
#     - bangcle_threat_data                                                                                                                                                  
#     - bangcle_threat_distinct_illegalapp_data                                                                                                                              
#     - bangcle_threat_illegal_app_statistical_learning 

# - name: 备份hbase-4.6 到宿主机
#   synchronize:
#     mode: pull
#     src: /bb_home/bb_hbase/backups
#     dest: "../backups/4602/hbase/"
#     # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
#     delete: yes
#     rsync_timeout: 600
#   ignore_errors: True
#   when: check_bangcle_user.stat.exists == True

# - name: 备份hbase-4.6
#   synchronize:
#     mode: pull
#     src: /bb_home/bb_hbase/data
#     dest: "../backups/4602/hbase/"
#     # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
#     rsync_timeout: 600
#   ignore_errors: True
#   when: check_bangcle_user.stat.exists == True

#### 4.6 spark
- name: check  /bb_home/bb_analyzerevent
  stat: 
    path: "/bb_home/bb_analyzerevent"
  register: check_bangcle_user
  ignore_errors: True

- name: 备份analyzerevent-4.6
  synchronize:
    mode: pull
    src: /bb_home/bb_analyzerevent/spark-1.6.0-bin-hadoop2.6
    dest: "../backups/4602/{{ ansible_default_ipv4['address'] }}/spark/"
    # rsync_opts: "--exclude=bb_docker,--exclude=bb_es,--exclude=*/log/*"
    rsync_timeout: 600
  ignore_errors: True
  when: check_bangcle_user.stat.exists == True

- name: wait for postgresql port
  wait_for:
    port: "{{ postgres_port }}"
    delay: 5
    state: started
    timeout: 10
  register: pg_port_outputs
  ignore_errors: True

#### 备份postgresql
# 判断4.6目录
- name: check /bb_home/bb_pg
  stat: 
    path: "/bb_home/bb_pg"
  register: check_bb_pg
  ignore_errors: True

- name: 4602版本postgreps数据导出
  shell: docker exec -it bb_pg pg_dump -U bangcle_pg bangcle_everisk_v4 > /tmp/bangcle_everisk_v4.sql
  when: check_bb_pg.stat.exists == True and pg_port_outputs.failed == false

- name: 创建备份目录
  file:
    path: "../backups/4602/data-export-postgres/"
    state: directory
    mode: 0777
  connection: local

- name: 将bangcle_everisk_v4.sql数据文件备份到ansible所在服务器上
  fetch:
    src: /tmp/bangcle_everisk_v4.sql
    dest: "../backups/4602/data-export-postgres/"
    flat: yes
  when: check_bb_pg.stat.exists == True and pg_port_outputs.failed == false

# 判断4.3目录,pg_dump命令需要使用安装目录里的/data/bangcle_pg/pgsql/bin/pg_dump
- name: check /home/<USER>
  stat: 
    path: "/home/<USER>"
  register: check_bangcle_pg
  ignore_errors: True

- name: 4.3版本postgreps数据库bangcle_everisk_v4,bangcle_cm,bangcle_rman导出
  shell: "{{ item }}"
  with_items:
    - /home/<USER>/pgsql/bin/pg_dump -U bangcle_pg bangcle_everisk_v4 > /tmp/bangcle_everisk_v4.sql
    - /home/<USER>/pgsql/bin/pg_dump -U bangcle_pg bangcle_cm > /tmp/bangcle_cm.sql
    - /home/<USER>/pgsql/bin/pg_dump -U bangcle_pg bangcle_rman > /tmp/bangcle_rman.sql
  when: check_bangcle_pg.stat.exists == True and pg_port_outputs.failed == false
  ignore_errors: True

- name: 将 bangcle_everisk_v4,bangcle_cm,bangcle_rman 数据文件备份到ansible所在服务器上
  fetch:
    src: "{{ item }}"
    dest: "../backups/4602/data-export-postgres/"
    flat: yes
  with_items:
    - /tmp/bangcle_everisk_v4.sql
    - /tmp/bangcle_cm.sql
    - /tmp/bangcle_rman.sql
  when: check_bangcle_pg.stat.exists == True and pg_port_outputs.failed == false
