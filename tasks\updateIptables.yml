---
### es漏洞
- name: 修复es-9200
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 9200
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local es-9200
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 9200
  ignore_errors: True
  become: yes

- name: 修复es-9200 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 9200
  ignore_errors: True
  become: yes

- name: 修复es-9201
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 9201
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local es-9201
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 9201
  ignore_errors: True
  become: yes

- name: 修复es-9201 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 9201
  ignore_errors: True
  become: yes

### zookeeper漏洞
- name: 修复zookeeper-2181
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 2181
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local zookeeper-2181
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 2181
  ignore_errors: True
  become: yes

- name: 修复zookeeper-2181 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 2181
  ignore_errors: True
  become: yes

### cdh漏洞
- name: 修复cdh-8086
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 8086
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local cdh-8086
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 8086
  ignore_errors: True
  become: yes

- name: 修复cdh-8086 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 8086
  ignore_errors: True
  become: yes

### hdfs漏洞
- name: 修复hdfs-50070
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 50070
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local hdfs-50070
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 50070
  ignore_errors: True
  become: yes

- name: 修复hdfs-50070 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 50070
  ignore_errors: True
  become: yes

### redis漏洞
- name: 修复redis-6379
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 6379
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local redis-6379
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 6379
  ignore_errors: True
  become: yes

- name: 修复redis-6379 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 6379
  ignore_errors: True
  become: yes

- name: 修复redis-6379 DROP
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER  
    protocol: tcp
    jump: DROP
    destination_port: 6379
  ignore_errors: True
  become: yes

- name: 修复redis-6379
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 6379
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local redis-6379
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 6379
  ignore_errors: True
  become: yes

### postgres漏洞
- name: 修复postgres-5432
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 5432
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local postgres-5432
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 5432
  ignore_errors: True
  become: yes

- name: 修复postgres-5432 DROP
  ansible.builtin.iptables:
    chain: INPUT
    protocol: tcp
    jump: DROP
    destination_port: 5432
  ignore_errors: True
  become: yes

- name: 修复postgres-5432 DROP
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER  
    protocol: tcp
    jump: DROP
    destination_port: 5432
  ignore_errors: True
  become: yes

- name: 修复postgres-5432
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 5432
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local postgres-5432
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 5432
  ignore_errors: True
  become: yes

# 修复crash
- name: 修复crash-5000 DROP
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER  
    protocol: tcp
    jump: DROP
    destination_port: 5000
  ignore_errors: True
  become: yes

- name: 修复crash-5000
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: "{{ item }}"
    jump: ACCEPT
    destination_port: 5000
  with_inventory_hostnames:
    - "{{ role_name }}"
  ignore_errors: True
  become: yes

- name: 修复local crash-5000
  ansible.builtin.iptables:
    action: insert
    chain: DOCKER 
    protocol: tcp
    source: 127.0.0.1
    jump: ACCEPT
    destination_port: 5000
  ignore_errors: True
  become: yes

#- name: copy rpm包
#  copy:
#    src: "{{ item }}"
#    dest: /tmp/rpms/
#    owner: "{{ bangcle_user }}"
#    group: "{{ bangcle_user }}"
#    force: yes
#    mode: 0644
#  with_fileglob:
#    - "tools/rpms/*.rpm"
#  register: rpms_copied

#- name: 输出rpms
#  debug:
#    msg: "{{ rpms_copied }}"

#- name: local RPMs not found
#  fail:
#    msg: "RPMs not found in ../files/"
#  when: rpms_copied.results|length == 0 and rpms_copied.skipped and rpms_copied.skipped_reason.find('No items') != -1

#- name: rpm包数据化
#  set_fact:
#    rpm_list: "{{ rpms_copied.results | map(attribute='dest') | list}}"

#- name: install RPMs
#  yum:
#    name: "{{rpm_list}}"

# - name: systemctl enable iptables
#   command: "systemctl enable iptables"

# - name: service  iptables save
#   command: "service  iptables save"
