---
#- tags: install_security-event-data-preparation

- import_tasks: createUser.yml

- name: 安装security-event-data-preparation包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ dataPreparation_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/security-event-data-preparation/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/security-event-data-preparation/application-zk.properties.j2"
    flat: yes

- name: 配置security-event-data-preparation的application-zk.properties
  template:
    src: security-event-data-preparation/application-zk.properties.j2
    dest: /home/<USER>/app/security-event-data-preparation/config/application-zk.properties

- name: 配置security-event-data-preparation的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/security-event-data-preparation/config/hbase-site.xml

- name: 调整security-event-data-preparation的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/security-event-data-preparation/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"
  notify:
    - restart security-event-data-preparation

- name: 提前重启服务
  meta: flush_handlers
