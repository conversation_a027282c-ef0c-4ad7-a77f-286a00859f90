---

- import_tasks: backupWxgzApp.yml
  vars:
    backup_server: init

- name: 将init重新打包
  archive:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init-modify.tar.gz"
    format: "gz"
    mode: 0644
  connection: local

- name: 安装init包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init-modify.tar.gz"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: 配置log4j2
  template:
    src: "log4j2/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'log4j2.xml.j2' , value: 'config/log4j2.xml' }

- name: 配置执行权限
  command: "chmod +x /home/<USER>/app/init/bin/service.sh"

- name: 调整init的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/init/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

- name: 创建images目录
  file:
    path: "/home/<USER>/app/init/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - images

- name: 传包-init镜像
  copy:
    src: "{{ init_img_tarball_name}}"
    dest: "/home/<USER>/app/init/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 加载images
  command: "docker load -i /home/<USER>/app/init/images/init.tar"

- name: 配置init的config文件
  template:
    src: "init/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length == 1

- name: 配置init的config文件
  template:
    src: "init/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.multi.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length > 1

- name: 解决init目录权限问题
  command: "chown {{ bangcle_user }}. -R /home/<USER>/app/init"

- name: ready to notify
  shell: ls
  notify:
    - restart init

- name: 提前重启服务
  meta: flush_handlers
