---
### 设置集群密码
- name: 配置设置elasticsearch密码的执行脚本es_user.sh
  template:
    src: xpack/es_user.sh.j2
    dest: /home/<USER>/server/elasticsearchMaster/bin/es_user.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  when: 
    - add_esxpack == true
    - inventory_hostname in groups['elasticsearchMaster'][0]

- name: 给elastic用户设置密码
  become: yes
  become_user: "{{ bangcle_user }}"
  shell: "bash /home/<USER>/server/elasticsearchMaster/bin/es_user.sh"
  environment:
    BASH_ENV: ~/.bash_profile
  ignore_errors: True
  when: 
    - add_esxpack == true
    - inventory_hostname in groups['elasticsearchMaster'][0]


# - name: 等待elasticsearchMaster启动建立集群时间
#   shell: "sleep 20s"
#   when: add_esxpack == true
# 
# - name: 删除es超级管理员用户
#   shell: "su - {{ bangcle_user }} -c \"/home/<USER>/server/elasticsearchMaster/elasticsearch/bin/elasticsearch-users userdel copriwolf\" "
#   ignore_errors: True
#   when: add_esxpack == true
# 
# - name: 添加es超级管理员用户
#   shell: "su - {{ bangcle_user }} -c \"/home/<USER>/server/elasticsearchMaster/elasticsearch/bin/elasticsearch-users useradd copriwolf -p sayHi2Elastic -r superuser\" "
#   when: add_esxpack == true
# 
# - name: 等待超级管理员可用
#   shell: "sleep 10s"
#   when: add_esxpack == true
# 
# - name: 设置es用户名elastic的密码
#   shell: "curl -u copriwolf:sayHi2Elastic -XPUT \"http://{{ inventory_hostname }}:9200/_xpack/security/user/elastic/_password?pretty\" -H 'Content-Type: application/json' -d '{\"password\": \"beap123\"}'"
#   when: add_esxpack == true

- name: restart transfer
  shell: ls
  notify:
    - restart transfer
  when: 
    - add_esxpack == true
    - inventory_hostname in groups['transfer']

- name: restart web-service
  shell: ls
  notify:
    - restart web-service
  when: 
    - add_esxpack == true
    - inventory_hostname in groups['web-service']

#- name: restart threat-index
#  shell: ls
#  notify:
#    - restart threat-index
#  when: 
#    - add_esxpack == true
#    - inventory_hostname in groups['threat-index']
