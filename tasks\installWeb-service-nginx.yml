---
# tags: deploy_redis

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- import_tasks: backupServer.yml
  vars:
    backup_server: web-service-nginx

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/web-service-nginx/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp
    - images/{{ ansible_architecture|lower }}

- name: 传包-web-service-nginx
  copy:
    src: "server/web-service-nginx/config"
    dest: "/home/<USER>/server/web-service-nginx/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 传包-web-service-nginx
  copy:
    src: "{{ webServiceNginx_tarball_name }}"
    dest: "/home/<USER>/server/web-service-nginx/images/{{ ansible_architecture|lower }}/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的日志
  file:
    path: "/home/<USER>/server/web-service-nginx/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    # recurse: yes
    state: touch
  with_items:
    - log/error.log

# - name: restart docker
#   command: "systemctl restart docker"

- name: load web-service-nginx image
  command: "docker load -i /home/<USER>/server/web-service-nginx/images/{{ ansible_architecture|lower }}/web-service-nginx.tar"
  
- import_tasks: checkIPv6.yml

- name: 配置web-service-nginx的config文件，当支持ipv6时
  template:
    src: "web-service-nginx/{{ item.key }}"
    dest: "/home/<USER>/server/web-service-nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'webService.conf.j2' , value: 'config/conf.d/webService.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "x86_64" and supports_ipv6

- name: 配置web-service-nginx的config文件，当不支持ipv6时
  template:
    src: "web-service-nginx/{{ item.key }}"
    dest: "/home/<USER>/server/web-service-nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'webService_ipv4.conf.j2' , value: 'config/conf.d/webService.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "x86_64" and not supports_ipv6

- name: 配置web-service-nginx服务配置文件及脚本，当支持ipv6时
  template:
    src: "web-service-nginx/{{ item.key }}"
    dest: "/home/<USER>/server/web-service-nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'webService_aarch64.conf.j2' , value: 'config/conf.d/webService.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "aarch64" and supports_ipv6

- name: 配置web-service-nginx服务配置文件及脚本，当不支持ipv6时
  template:
    src: "web-service-nginx/{{ item.key }}"
    dest: "/home/<USER>/server/web-service-nginx/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'webService_aarch64_ipv4.conf.j2' , value: 'config/conf.d/webService.conf' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "aarch64" and not supports_ipv6

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/web-service-nginx/bin/service.sh"
    path: "/home/<USER>/server/web-service-nginx/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/web-service-nginx/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "web-service-nginx/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/web-service-nginx/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: list
  shell: ls
  notify:
    - restart web-service-nginx
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
