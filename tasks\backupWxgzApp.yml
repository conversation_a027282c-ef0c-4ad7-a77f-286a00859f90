---

- name: check  {{ backup_server }}是否安装
  stat: 
    path: "/home/<USER>/app/{{ backup_server }}/"
  register: check_app_server
  ignore_errors: True

- name: check  {{ backup_server }}是否已经备份
  stat: 
    path: "/home/<USER>/backups/{{ backup_server }}.bk-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}"
  register: check_server_back
  ignore_errors: True

- name: 备份{{ backup_server }}服务
  command: "cp -a /home/<USER>/app/{{ backup_server }} /home/<USER>/backups/{{ backup_server }}.bk-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}"
  notify:
    - stop {{ backup_server }}
  when: 
    - check_app_server.stat.exists == True
    - check_server_back.stat.exists != True

- name: 提前停止服务
  meta: flush_handlers
  when:  
    - check_app_server.stat.exists == True
    # - check_server_back.stat.exists != True
  ignore_errors: True

- name: drop {{ backup_server }}
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/home/<USER>/app/{{ backup_server }}"
    #- "/home/<USER>/backups/{{ backup_server }}.bk-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}/log"
  when:  
    - check_app_server.stat.exists == True
    # - check_server_back.stat.exists != True
  ignore_errors: True

- name: drop log directory
  shell: ls
  args:
    chdir: /home/<USER>/backups/{{ backup_server }}.bk-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}/log
  register: file_list

- name: clean
  file:
    path: /home/<USER>/backups/{{ backup_server }}.bk-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}/log/{{ item }}
    state: absent
  with_items:
    - "{{ file_list.stdout_lines }}"
  become: true
  when:
    - check_app_server.stat.exists == True
    # - check_server_back.stat.exists != True
  ignore_errors: True
