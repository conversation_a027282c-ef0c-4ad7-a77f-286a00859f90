---
#- tags: install_restfulService

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 安装restfulService包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ restfulservice_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/restfulService/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/restfulService/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/restfulService/application-zk.properties.j2"
    flat: yes
  when: check_application_zk.stat.exists == True

- name: 配置restfulService的application-zk.properties
  template:
    src: restfulService/application-zk.properties.j2
    dest: /home/<USER>/app/restfulService/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_zk.stat.exists == True

- name: check  application-remote.properties
  stat: 
    path: "/home/<USER>/app/restfulService/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/restfulService/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/restfulService/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置restfulService的application-remote.properties
  template:
    src: restfulService/application-remote.properties.j2
    dest: /home/<USER>/app/restfulService/config/application-remote.properties
    trim_blocks: no 
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_remote.stat.exists == True

# - name: 配置restfulService的application-zk.properties
#   template:
#     src: restfulService/application-zk.properties.j2
#     dest: /home/<USER>/app/restfulService/config/application-zk.properties

# - name: 配置restfulService的hbase-site.xml
#   template:
#     src: hbase-site.xml.j2
#     dest: /home/<USER>/app/restfulService/config/hbase-site.xml

# - name: 调整restfulService的启动用户
#   lineinfile: 
#     dest: "/home/<USER>/app/restfulService/bin/service.sh"
#     regexp: "^START_USER="
#     line: "START_USER={{ bangcle_user }}"
  # notify:
  #   - restart restfulService
