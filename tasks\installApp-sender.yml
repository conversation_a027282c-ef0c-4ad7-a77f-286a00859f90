---

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
#- import_tasks: backupApp.yml
#  vars:
#    backup_server: app-sender

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/app/app-sender/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp

- name: 传包-app-sender
  copy:
    src: "{{ appSender_tarball_name}}"
    dest: "/home/<USER>/app/app-sender/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  
- name: 配置app-sender的config文件
  template:
    src: "app-sender/{{ item.key }}"
    dest: "/home/<USER>/app/app-sender/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'application-standard.properties.j2' , value: 'config/application-standard.properties' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }

- name: 配置log4j2
  template:
    src: "log4j2/{{ item.key }}"
    dest: "/home/<USER>/app/app-sender/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'log4j2.xml.j2' , value: 'config/log4j2.xml' }

- name: 加载images
  command: "docker load -i /home/<USER>/app/app-sender/images/app-sender.tar"


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/app-sender/bin/service.sh"
    path: "/home/<USER>/app/app-sender/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/app-sender/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "app-sender/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/app-sender/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart app-sender

- name: 提前重启服务
  meta: flush_handlers
