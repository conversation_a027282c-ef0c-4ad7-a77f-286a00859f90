---
# tags: deploy_redis

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: copy redisCluster
  copy:
    src: "server/redisCluster"
    dest: "/home/<USER>/server/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/redisCluster/bin"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory

- name: load redis image
  command: "docker load -i /home/<USER>/server/redisCluster/images/redis.tar"
  
- name: restart docker
  command: "systemctl restart docker"
  
- name: 配置redis服务配置文件及脚本
  template:
    src: "redisCluster/{{ item.key }}"
    dest: "/home/<USER>/server/redisCluster/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'redis.conf.j2' , value: 'conf/redis.conf' }
    - { key: 'createCluster.sh.j2' , value: 'bin/createCluster.sh'}
  notify:
    - restart redisCluster

- name: 提前重启服务
  meta: flush_handlers
