---
# tags: install_filebeat

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 创建filebeat的目录
  file:
    path: "/home/<USER>/ops/filebeat/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - data
    - bin


- name: 安装filebeat包
  unarchive:
    src: "{{ filebeat_tarball_name }}"
    dest: "/home/<USER>/ops/filebeat"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  # notify:
  #   - restart filebeat

- name: filebeat软连接到/home/<USER>/ops/filebeat/filebeat
  file:
    src: "/home/<USER>/ops/filebeat/{{ filebeat_name }}"
    dest: "/home/<USER>/ops/filebeat/filebeat"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False

- name: 配置filebeat服务配置文件及脚本
  template:
    src: "filebeat/{{ item.key }}"
    dest: "/home/<USER>/ops/filebeat/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'filebeat.yml.j2' , value: 'filebeat/filebeat.yml' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'filebeat_dog.sh.j2' , value: 'bin/filebeat_dog.sh' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/ops/filebeat/bin/service.sh"
    path: "/home/<USER>/ops/filebeat/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/ops/filebeat/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "filebeat/service_supervisor.sh.j2"
    dest: "/home/<USER>/ops/filebeat/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart filebeat
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
