---
#- tags: install_esDataAggregater

- import_tasks: createUser.yml

- name: 安装esDataAggregater包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ esdataaggregater_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/esDataAggregater/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/esDataAggregater/application-zk.properties.j2"
    flat: yes

- name: 配置esDataAggregater的application-zk.properties
  template:
    src: esDataAggregater/application-zk.properties.j2
    dest: /home/<USER>/app/esDataAggregater/config/application-zk.properties

- name: 配置esDataAggregater的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/esDataAggregater/config/hbase-site.xml

- name: 调整esDataAggregater的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/esDataAggregater/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"
  notify:
    - restart esDataAggregater

- name: 定时任务esDataAggregater
  cron:
    minute: 0
    hour: 2
    job: "su - {{ bangcle_user }} -c \" source ~/.bash_profile && ./app/esDataAggregater/bin/service.sh start \" "
    state: present
