---

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
- import_tasks: backupApp.yml
  vars:
    backup_server: dev-mark

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/app/dev-mark/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp

- name: 传包-dev-mark
  copy:
    src: "{{ devMark_tarball_name}}"
    dest: "/home/<USER>/app/dev-mark/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  
- name: 配置dev-mark的config文件
  template:
    src: "dev-mark/{{ item.key }}"
    dest: "/home/<USER>/app/dev-mark/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }

- name: 配置log4j2
  template:
    src: "log4j2/{{ item.key }}"
    dest: "/home/<USER>/app/dev-mark/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'log4j2.xml.j2' , value: 'config/log4j2.xml' }

- name: 加载images
  command: "docker load -i /home/<USER>/app/dev-mark/images/dev-mark.tar"

- name: configure startup.sh
  template:
    src: "dev-mark/startup.sh.j2"
    dest: "/home/<USER>/app/dev-mark/bin/startup.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/dev-mark/bin/service.sh"
    path: "/home/<USER>/app/dev-mark/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/dev-mark/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "dev-mark/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/dev-mark/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart dev-mark
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
