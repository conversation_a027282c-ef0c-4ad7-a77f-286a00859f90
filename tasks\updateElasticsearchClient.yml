# tags: update_elasticsearchclient
- name: 停止elasticsearchClient服务
  become_user: "{{ bangcle_user }}"
  shell: "bash /home/<USER>/server/elasticsearchClient/bin/service.sh stop && sleep 20"
  ignore_errors: True

- name: 判断部署目录是否有6.8.5版本
  stat:
    path: "/home/<USER>/server/elasticsearchClient/elasticsearch-6.8.5"
  register: elasticsearchClient_status

- name: 备份elasticsearchClient服务程序文件为6.0.0版本
  become_user: "{{ bangcle_user }}"
  shell: "mv /home/<USER>/server/elasticsearchClient/elasticsearch{,-6.0.0}"
  ignore_errors: True
  when: elasticsearchClient_status.stat.exists != True

- name: 拷贝新版本elasticsearch-6.8.5到运行目录下
  unarchive:
    src: "{{ elasticsearchXpack_tarball_name }}"
    dest: "/home/<USER>/server/elasticsearchClient"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  when: elasticsearchClient_status.stat.exists != True

- name: elasticsearchClient软连接到/home/<USER>/server/elasticsearchClient/elasticsearch
  file:
    src: "/home/<USER>/server/elasticsearchClient/elasticsearch-6.8.5"
    dest: "/home/<USER>/server/elasticsearchClient/elasticsearch"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: elasticsearchClient_status.stat.exists != True

- name: 将elasticsearchClient原配置文件拷贝到新版本中,避免原配置文件已优化
  become_user: "{{ bangcle_user }}"
  shell: "cp -af /home/<USER>/server/elasticsearchClient/elasticsearch-6.0.0/config/{elasticsearch.yml,jvm.options}  /home/<USER>/server/elasticsearchClient/elasticsearch/config/"
  when: elasticsearchClient_status.stat.exists != True

- name: chmod elasticsearchClient bin
  shell: "chmod -R 755 /home/<USER>/server/elasticsearchClient/elasticsearch/bin"
  when: elasticsearchClient_status.stat.exists != True

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/elasticsearchClient/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - logs
    - data
    - plugins

- name: copy mapper-murmur3
  copy:
    src: "{{ elasticsearch_mapper_murmur3_tarball_name }}"
    dest: "/home/<USER>/server/elasticsearchClient/plugins"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: 安装 mapper-murmur3
  become_user: "{{ bangcle_user }}"
  shell: "/home/<USER>/server/elasticsearchClient/elasticsearch/bin/elasticsearch-plugin install file:///home/<USER>/server/elasticsearchClient/plugins/mapper-murmur3-6.8.5.zip"
  ignore_errors: True

- name: 在elasticsearchClient配置文件elasticsearch.yml增加xpack认证
  become_user: "{{ bangcle_user }}"
  lineinfile:
    path: /home/<USER>/server/elasticsearchClient/elasticsearch/config/elasticsearch.yml
    line: "{{ item }}"
  with_items:
    - 'xpack.security.enabled: true'
    - 'xpack.security.transport.ssl.enabled: true'
    - 'xpack.security.transport.ssl.key:  certs/bangcle.key'
    - 'xpack.security.transport.ssl.certificate: certs/bangcle.crt'
    - 'xpack.security.transport.ssl.certificate_authorities: certs/ca.crt'
    - 'xpack.security.transport.ssl.verification_mode: certificate'

- name: 配置es的key文件
  template:
    src: "xpack/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/elasticsearch/config/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  with_items:
    - { key: 'bangcle.crt.j2' , value: 'certs/bangcle.crt' }
    - { key: 'bangcle.key.j2' , value: 'certs/bangcle.key' }
    - { key: 'ca.crt.j2' , value: 'certs/ca.crt' }
    - { key: 'ca.key.j2' , value: 'certs/ca.key' }

- name: 启动elasticsearchClient服务
  shell: "ls -al"
  notify:
    - restart elasticsearchClient
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers

