---
- import_tasks: rollbackInit.yml
  vars:
    rollback_app: init
  tags: 
    - never
    - rollback
    - rollback_init
  when: inventory_hostname in groups['init']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: receiver
  tags: 
    - never
    - rollback
    - rollback_receiver
  when: inventory_hostname in groups['receiver']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: cleaner
  tags: 
    - never
    - rollback
    - rollback_cleaner
  when: inventory_hostname in groups['cleaner']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: transfer
  tags: 
    - never
    - rollback
    - rollback_transfer
  when: inventory_hostname in groups['transfer']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: threat
  tags: 
    - never
    - rollback
    - rollback_threat
  when: inventory_hostname in groups['threat']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: security-event
  tags: 
    - never
    - rollback
    - rollback_security-event
  when: inventory_hostname in groups['security-event']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: threat-index
  tags: 
    - never
    - rollback
    - rollback_threat-index
  when: inventory_hostname in groups['threat-index']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: analyzer-dev
  tags: 
    - never
    - rollback
    - rollback_analyzer-dev
  when: inventory_hostname in groups['analyzer-dev']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: analyzer-relation
  tags: 
    - never
    - rollback
    - rollback_analyzer-relation
  when: inventory_hostname in groups['analyzer-relation']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: web-service
  tags: 
    - never
    - rollback
    - rollback_web-service
  when: inventory_hostname in groups['web-service']

- import_tasks: rollbackServer.yml
  vars:
    rollback_server: web-service-nginx
  tags: 
    - never
    - rollback
    - rollback_web-service
    - rollback_web-service-nginx
  when: inventory_hostname in groups['web-service']

# - import_tasks: rollbackApp.yml
#   vars:
#     rollback_app: dev-mark
#   tags: 
#     - never
#     - rollback
#     - rollback_dev-mark
#   when: inventory_hostname in groups['dev-mark']

- import_tasks: rollbackApp.yml
  vars:
    rollback_app: app-sender
  tags: 
    - never
    - rollback
    - rollback_app-sender
  when: inventory_hostname in groups['app-sender']