#!/bin/bash

# 通用服务脚本模板
# 适用于所有应用组件的标准化服务管理

# =============================================================================
# 配置部分 - 根据具体服务修改
# =============================================================================

# 服务基本信息
SERVICE_NAME="your-service-name"        # 修改为实际服务名
SERVICE_PORT="8080"                     # 修改为实际端口
SERVICE_USER="appuser"                  # 运行用户

# 路径配置
SERVICE_HOME="/export/app/$SERVICE_NAME"
JAVA_HOME="/export/tools/jdk"           # JDK 8 路径
PID_FILE="$SERVICE_HOME/$SERVICE_NAME.pid"
LOG_FILE="$SERVICE_HOME/log/$SERVICE_NAME.log"
ERROR_LOG="$SERVICE_HOME/log/$SERVICE_NAME-error.log"

# JAR文件配置
JAR_FILE="$SERVICE_HOME/lib/$SERVICE_NAME.jar"
CONFIG_FILE="$SERVICE_HOME/config/application.properties"

# JVM参数配置 - 根据服务规模调整
JVM_OPTS="-Xmx2g -Xms2g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC"
JVM_OPTS="$JVM_OPTS -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=$SERVICE_HOME/log/"
JVM_OPTS="$JVM_OPTS -Dspring.config.location=$CONFIG_FILE"
JVM_OPTS="$JVM_OPTS -Dlogging.config=$SERVICE_HOME/config/log4j2.xml"

# 启动超时时间（秒）
START_TIMEOUT=60
STOP_TIMEOUT=30

# =============================================================================
# 工具函数
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查环境
check_environment() {
    local errors=0
    
    # 检查Java环境
    if [ ! -x "$JAVA_HOME/bin/java" ]; then
        log_error "Java not found at $JAVA_HOME/bin/java"
        ((errors++))
    fi
    
    # 检查JAR文件
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR file not found: $JAR_FILE"
        ((errors++))
    fi
    
    # 检查配置文件
    if [ ! -f "$CONFIG_FILE" ]; then
        log_error "Config file not found: $CONFIG_FILE"
        ((errors++))
    fi
    
    # 检查日志目录
    if [ ! -d "$(dirname "$LOG_FILE")" ]; then
        log_warning "Log directory not found, creating: $(dirname "$LOG_FILE")"
        mkdir -p "$(dirname "$LOG_FILE")"
    fi
    
    # 检查权限
    if [ ! -w "$(dirname "$PID_FILE")" ]; then
        log_error "No write permission for PID directory: $(dirname "$PID_FILE")"
        ((errors++))
    fi
    
    return $errors
}

# 获取进程ID
get_pid() {
    if [ -f "$PID_FILE" ]; then
        cat "$PID_FILE"
    else
        echo ""
    fi
}

# 检查进程是否运行
is_running() {
    local pid=$(get_pid)
    if [ -n "$pid" ] && ps -p "$pid" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 等待进程启动
wait_for_start() {
    local timeout=$1
    local count=0
    
    log_info "等待服务启动..."
    
    while [ $count -lt $timeout ]; do
        if is_running; then
            # 检查端口是否监听
            if netstat -tlnp 2>/dev/null | grep ":$SERVICE_PORT " > /dev/null; then
                log_success "服务启动成功，端口 $SERVICE_PORT 已监听"
                return 0
            fi
        fi
        
        sleep 1
        ((count++))
        
        # 每10秒显示一次进度
        if [ $((count % 10)) -eq 0 ]; then
            log_info "等待中... ($count/$timeout 秒)"
        fi
    done
    
    log_error "服务启动超时 ($timeout 秒)"
    return 1
}

# 等待进程停止
wait_for_stop() {
    local timeout=$1
    local count=0
    
    log_info "等待服务停止..."
    
    while [ $count -lt $timeout ]; do
        if ! is_running; then
            log_success "服务已停止"
            return 0
        fi
        
        sleep 1
        ((count++))
        
        # 每5秒显示一次进度
        if [ $((count % 5)) -eq 0 ]; then
            log_info "等待中... ($count/$timeout 秒)"
        fi
    done
    
    log_error "服务停止超时 ($timeout 秒)"
    return 1
}

# =============================================================================
# 服务操作函数
# =============================================================================

# 启动服务
start_service() {
    log_info "启动 $SERVICE_NAME 服务..."
    
    # 检查环境
    if ! check_environment; then
        log_error "环境检查失败，无法启动服务"
        return 1
    fi
    
    # 检查是否已经运行
    if is_running; then
        local pid=$(get_pid)
        log_warning "服务已经在运行 (PID: $pid)"
        return 0
    fi
    
    # 清理旧的PID文件
    if [ -f "$PID_FILE" ]; then
        log_info "清理旧的PID文件: $PID_FILE"
        rm -f "$PID_FILE"
    fi
    
    # 切换到服务目录
    cd "$SERVICE_HOME" || {
        log_error "无法切换到服务目录: $SERVICE_HOME"
        return 1
    }
    
    # 构建启动命令
    local cmd="$JAVA_HOME/bin/java $JVM_OPTS -jar $JAR_FILE"
    
    log_info "执行命令: $cmd"
    log_info "日志文件: $LOG_FILE"
    log_info "错误日志: $ERROR_LOG"
    
    # 启动服务
    nohup $cmd > "$LOG_FILE" 2> "$ERROR_LOG" &
    local pid=$!
    
    # 保存PID
    echo $pid > "$PID_FILE"
    log_info "服务已启动，PID: $pid"
    
    # 等待启动完成
    if wait_for_start $START_TIMEOUT; then
        log_success "$SERVICE_NAME 启动成功"
        return 0
    else
        log_error "$SERVICE_NAME 启动失败"
        # 显示错误日志的最后几行
        if [ -f "$ERROR_LOG" ] && [ -s "$ERROR_LOG" ]; then
            log_error "错误日志内容:"
            tail -10 "$ERROR_LOG"
        fi
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 $SERVICE_NAME 服务..."
    
    if ! is_running; then
        log_warning "服务未运行"
        # 清理可能存在的PID文件
        if [ -f "$PID_FILE" ]; then
            rm -f "$PID_FILE"
        fi
        return 0
    fi
    
    local pid=$(get_pid)
    log_info "正在停止服务 (PID: $pid)..."
    
    # 发送TERM信号
    kill -TERM $pid
    
    # 等待进程停止
    if wait_for_stop $STOP_TIMEOUT; then
        rm -f "$PID_FILE"
        log_success "$SERVICE_NAME 已停止"
        return 0
    else
        log_warning "优雅停止失败，强制终止进程..."
        kill -KILL $pid 2>/dev/null
        sleep 2
        
        if ! is_running; then
            rm -f "$PID_FILE"
            log_success "$SERVICE_NAME 已强制停止"
            return 0
        else
            log_error "无法停止 $SERVICE_NAME"
            return 1
        fi
    fi
}

# 重启服务
restart_service() {
    log_info "重启 $SERVICE_NAME 服务..."
    
    if is_running; then
        if ! stop_service; then
            log_error "停止服务失败，无法重启"
            return 1
        fi
    fi
    
    # 等待一段时间确保端口释放
    sleep 3
    
    start_service
}

# 查看服务状态
status_service() {
    echo "========================================="
    echo "服务名称: $SERVICE_NAME"
    echo "服务端口: $SERVICE_PORT"
    echo "服务目录: $SERVICE_HOME"
    echo "PID文件:  $PID_FILE"
    echo "日志文件: $LOG_FILE"
    echo "========================================="
    
    if is_running; then
        local pid=$(get_pid)
        log_success "服务正在运行 (PID: $pid)"
        
        # 显示进程信息
        echo "进程信息:"
        ps -p $pid -o pid,ppid,user,start,time,command 2>/dev/null || echo "无法获取进程信息"
        
        # 检查端口监听
        echo ""
        echo "端口监听状态:"
        if netstat -tlnp 2>/dev/null | grep ":$SERVICE_PORT "; then
            log_success "端口 $SERVICE_PORT 正在监听"
        else
            log_warning "端口 $SERVICE_PORT 未监听"
        fi
        
        # 显示内存使用
        echo ""
        echo "内存使用:"
        ps -p $pid -o pid,vsz,rss,pmem 2>/dev/null || echo "无法获取内存信息"
        
    else
        log_error "服务未运行"
        
        # 检查是否有残留的PID文件
        if [ -f "$PID_FILE" ]; then
            log_warning "发现残留的PID文件: $PID_FILE"
        fi
    fi
    
    # 显示最近的日志
    echo ""
    echo "最近的日志 (最后10行):"
    if [ -f "$LOG_FILE" ]; then
        tail -10 "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 查看日志
logs_service() {
    if [ -f "$LOG_FILE" ]; then
        log_info "显示 $SERVICE_NAME 日志 (按 Ctrl+C 退出):"
        tail -f "$LOG_FILE"
    else
        log_error "日志文件不存在: $LOG_FILE"
        return 1
    fi
}

# =============================================================================
# 主程序
# =============================================================================

# 检查参数
if [ $# -eq 0 ]; then
    echo "用法: $0 {start|stop|restart|status|logs}"
    echo ""
    echo "操作说明:"
    echo "  start   - 启动服务"
    echo "  stop    - 停止服务"
    echo "  restart - 重启服务"
    echo "  status  - 查看服务状态"
    echo "  logs    - 查看服务日志"
    exit 1
fi

# 执行操作
case "$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        logs_service
        ;;
    *)
        echo "错误: 未知操作 '$1'"
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac

exit $?
