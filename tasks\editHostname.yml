---

- name: get current hostname
  shell: hostname
  register: shelloutput

- name: debug current name
  debug:
    msg: "{{ shelloutput.stdout_lines[0] }}"

- name: set vars
  set_fact:
    currentname: "{{ shelloutput.stdout_lines[0] }}"
    hostsubstring: "localhost"

- name: true or false
  debug:
    msg: "contains localhost"
  when: hostsubstring in currentname

- name: 设置hostname
  block:
    - name: 设置hostname
      hostname:
        name: vm-{{ ansible_default_ipv4.address.split('.')[-1] }}
  rescue:
    - name: 使用hostnamectl设置主机名
      command: hostnamectl set-hostname vm-{{ ansible_default_ipv4.address.split('.')[-1] }}
  when: hostsubstring in currentname

- name: set new hostname
  set_fact:
    currentname: "vm-{{ ansible_default_ipv4.address.split('.')[-1] }}"
  when: hostsubstring in currentname

- name: copy hosts.j2 to all #将新的hosts.j2分发到各个节点上
  template: 
    src: bangcleUser/hosts.j2 
    dest: /etc/hosts
    backup: yes

- name: show all ip in the group
  shell: sed -i "3i\{{ item }} {{ hostvars[item]['ansible_hostname'] }}" /etc/hosts
  loop: "{{ groups['everisk-deploy'] }}"
