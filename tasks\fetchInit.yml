---

- name: Ensure local directory exists
  file:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/initold"
    state: directory
  connection: local

- name: empty local directory
  shell: rm -rf initold/*
  args:
    warn: false
    chdir: "{{ inventory_dir }}/roles/{{ role_name }}/files/app"
  connection: local

- name: Archive remote directory
  archive:
    path: /home/<USER>/app/init/config
    dest: /tmp/initold.tar.gz
    format: gz

- name: Fetch directory from remote server
  fetch:
    src: "/tmp/initold.tar.gz"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/initold/"
    flat: yes

- name: Unarchive the fetched file (run on localhost)
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/initold/initold.tar.gz"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/initold"
    remote_src: no
  delegate_to: localhost

- name: remove initold.tar.gz
  file:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/initold/initold.tar.gz"
    state: absent
  delegate_to: localhost

- name: 删除init历史文件
  file:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init"
    state: absent
  ignore_errors: True
  connection: local

- name: 解压init包到本地 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/{{ init_tarball_name }}"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/"
  connection: local

- name: 查找所有的配置文件
  find:
    paths: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init/config/"
    # paths: "/home/<USER>/app/init"
    recurse: yes
    file_type: file
    patterns: [ "everisk-v*.json","everisk.json","application*.properties","hbase-site.xml" ]
  register: config_files
  connection: local

- name: 返回config_files变量
  debug:
    msg: "{{ config_files }}"

- name: 替换配置文件中的占位符
  template:
    src: "{{ item }}"
    dest: "{{ item }}"
    trim_blocks: no
  with_items: "{{ config_files.files | map(attribute='path') | list }}"
  connection: local

