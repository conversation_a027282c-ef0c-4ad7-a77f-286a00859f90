### 设置集群密码
- name: 配置设置elasticsearch密码的执行脚本es_user.sh
  template:
    src: xpack/es_user.sh.j2
    dest: /home/<USER>/server/elasticsearchMaster/bin/es_user.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: sleep:等待elasticsearchMaster启动建立集群时间
  shell: "sleep 20s"

- name: 判断elasticsearch集群状态
  shell: "n=1;while ((n<=5)); do code=`curl -s {{ inventory_hostname }}:9200?pretty=true | grep status |awk '{print $3}'`; if (( ${code} == 401 )); then echo \"401\" && break; else ((n++)); sleep 60s; fi; if ((${n} == 5)); then echo \"${code}\" && break; fi; done;"
  register: check_elasticsearch_status
  ignore_errors: True

- name: 查看访问集群返回码
  debug:
    var: check_elasticsearch_status.stdout

- name: 步骤一:删除es超级管理员用户
  shell: "su - {{ bangcle_user }} -c \"/home/<USER>/server/elasticsearchMaster/elasticsearch/bin/elasticsearch-users userdel copriwolf\" "
  register: update_security_status1
  ignore_errors: True
  when: check_elasticsearch_status.stdout == "401"

- name: 查看步骤一执行状态
  debug:
    var: update_security_status1.stdout

- name: 步骤二:添加es超级管理员用户
  shell: "su - {{ bangcle_user }} -c \"/home/<USER>/server/elasticsearchMaster/elasticsearch/bin/elasticsearch-users useradd copriwolf -p sayHi2Elastic -r superuser\" "
  register: update_security_status2
  when: check_elasticsearch_status.stdout == "401"

- name: 看步骤二执行状态
  debug:
    var: update_security_status2.stdout

- name: sleep:等待超级管理员可用
  shell: "sleep 10s"

- name: 步骤三:执行设置es用户名elastic的密码
  shell: "curl -u copriwolf:sayHi2Elastic -XPUT \"http://{{ inventory_hostname }}:9200/_xpack/security/user/elastic/_password?pretty\" -H 'Content-Type: application/json' -d '{\"password\": \"beap123\"}'"
  register: update_security_status3
  ignore_errors: True
  when: check_elasticsearch_status.stdout == "401"

- name: 查看步骤三执行状态
  debug:
    var: update_security_status3.stdout

- name: 当请求集群返回不为401时,集群不可用,无法设置用户名和密码
  debug:
    msg: "请检查elasticsearch状态"
  when: check_elasticsearch_status.stdout != "401"  

