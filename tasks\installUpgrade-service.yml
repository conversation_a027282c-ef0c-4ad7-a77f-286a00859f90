---

- name: check users
  stat: 
    path: "/home/<USER>/ops"
  register: users_output

- import_tasks: createUser.yml
  when: users_output.stat.exists != True

- name: 解压upgrade-service包到本地 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/ops/upgrade-service/upgrade-service.tar.gz"
    dest: "/home/<USER>/ops/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"


- name: 配置upgrade-service的配置文件application.yml
  template:
    force: yes
    src: upgrade-service/application.yml.j2
    dest: /home/<USER>/ops/upgrade-service/config/application.yml
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
    

- name: 配置upgrade-service的配置文件application-prod.yml
  template:
    force: yes
    src: upgrade-service/application-prod.yml.j2
    dest: /home/<USER>/ops/upgrade-service/config/application-prod.yml
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: 配置upgrade-service的bin启动脚本service.sh
  template:
    force: yes
    src: upgrade-service/service.sh.j2
    dest: /home/<USER>/ops/upgrade-service/bin/service.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: 配置upgrade-service的bin启动脚本bangcle_dog.sh
  template:
    force: yes
    src: upgrade-service/bangcle_dog.sh.j2
    dest: /home/<USER>/ops/upgrade-service/bin/bangcle_dog.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: 配置upgrade-service的bin启动脚本startup.sh
  template:
    force: yes
    src: upgrade-service/startup.sh.j2
    dest: /home/<USER>/ops/upgrade-service/bin/startup.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: ready to notify
  shell: ls
  notify:
    - restart upgrade-service

- name: 提前重启服务
  meta: flush_handlers