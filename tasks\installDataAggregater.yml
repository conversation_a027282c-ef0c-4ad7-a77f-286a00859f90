---
#- tags: install_dataAggregater

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
- import_tasks: backupApp.yml
  vars: 
    backup_server: dataAggregater

- name: 安装dataAggregater包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ dataaggregater_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/dataAggregater/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/dataAggregater/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/dataAggregater/application-zk.properties.j2"
    flat: yes
  when: check_application_zk.stat.exists == True

- name: 配置dataAggregater的application-zk.properties
  template:
    src: dataAggregater/application-zk.properties.j2
    dest: /home/<USER>/app/dataAggregater/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_zk.stat.exists == True

- name: check  application-remote.properties
  stat: 
    path: "/home/<USER>/app/dataAggregater/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/dataAggregater/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/dataAggregater/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置dataAggregater的application-remote.properties
  template:
    src: dataAggregater/application-remote.properties.j2
    dest: /home/<USER>/app/dataAggregater/config/application-remote.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_remote.stat.exists == True

- name: 配置dataAggregater的hbase-site.xml
  template:
    src: hbase-site.xml.j2
    dest: /home/<USER>/app/dataAggregater/config/hbase-site.xml

- name: check  /tmp/4602/hdfs-site.xml
  stat: 
    path: "/tmp/4602/hdfs-site.xml"
  register: check_cdh
  connection: local
  ignore_errors: True

- name: CDH版本配置文件
  copy:
    src: "/tmp/4602/{{ item }}"
    dest: "/home/<USER>/app/dataAggregater/config/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - "core-site.xml"
    - "hbase-site.xml"
    - "hdfs-site.xml"
  when: check_cdh.stat.exists == True

- name: 调整dataAggregater的zk地址
  lineinfile: 
    dest: "/home/<USER>/app/dataAggregater/config/application-zk.properties"
    regexp: "^everisk.config.zk.host="
    line: "everisk.config.zk.host={{ zookeeper_addr }}"
  when: zookeeper_addr != None

- name: 调整dataAggregater的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/dataAggregater/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

- name: 定时任务dataAggregater
  #become_user: "{{ bangcle_user }}"
  cron:
    name: 威胁感知定时任务
    minute: 0
    hour: 2
    user: "{{ bangcle_user }}"
    job: " source ~/.bash_profile && /home/<USER>/app/dataAggregater/bin/service.sh start  "
    state: present
