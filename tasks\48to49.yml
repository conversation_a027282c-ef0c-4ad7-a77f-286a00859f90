---

# 备份服务
- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - backups/489
  ignore_errors: True

- name: 备份cesi
  command: "mv /home/<USER>/ops/cesi /home/<USER>/backups/489/"
  ignore_errors: True 
  
- name: 备份supervisor
  command: "mv /home/<USER>/ops/supervisor /home/<USER>/backups/489/"
  ignore_errors: True 

- name: 再次kill进程，防止服务未停止
  command: "su - {{ bangcle_user }} -c \"for i in `ls app`;do cd app/${i}/bin;./service.sh stop; cd - ; done\""
  # command: "for i in `ls app`;do cd app/${i}/bin;./service.sh stop; cd - ; done"
  become: yes
  # become_user: "{{ bangcle_user }}"
  ignore_errors: True

- name: 再次kill进程，防止服务未停止
  shell: "ps -ef |grep /app|awk '{print $2}'|xargs -i kill {}"
  become: yes
  # become_user: "{{ bangcle_user }}"
  ignore_errors: True

- name: 升级docker-compose
  copy:
    src: "bin/docker-compose/{{ ansible_architecture|lower }}/docker-compose"
    dest: "/home/<USER>/bin/docker-compose"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: check docker
  stat: 
    path: "/usr/bin/docker"
  register: docker_output

- block: 
  - name: 调整docker的limit限制
    template:
      src: "docker/docker.service.j2"
      dest: "/etc/systemd/system/docker.service"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755

  - name: 配置/etc/docker/daemon.json
    template:
      src: "docker/daemon.json.j2"
      dest: "/etc/docker/daemon.json"
      force: yes
      mode: 0755
    notify:
      - restart docker
  when: docker_output.stat.exists == True

- name: 备份app服务
  shell: "mv /home/<USER>/app/* /home/<USER>/backups/489/"
  become: yes
  become_user: "{{ bangcle_user }}" 
  ignore_errors: True

- name: 停止webService-nginx
  shell: "su -  {{ bangcle_user }} -c \"cd server/webService-nginx/bin; ./service.sh stop\""
  ignore_errors: True

- name: 备份webService-nginx
  shell: "mv /home/<USER>/server/webService-nginx /home/<USER>/backups/489/"
  become: yes
  become_user: "{{ bangcle_user }}" 
  ignore_errors: True

- name: 配置elasticsearchClient服务配置文件及脚本
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchClient/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'startup_elasticsearchClient.sh.j2' , value: 'bin/startup.sh' }
  notify:
    - restart elasticsearchClient
  when:
    - inventory_hostname in groups['elasticsearchClient']

- name: 配置elasticsearchMaster服务配置文件及脚本
  template:
    src: "elasticsearch/{{ item.key }}"
    dest: "/home/<USER>/server/elasticsearchMaster/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'startup_elasticsearchMaster.sh.j2' , value: 'bin/startup.sh' }
  notify:
    - restart elasticsearchMaster
  when:
    - inventory_hostname in groups['elasticsearchMaster']

### 升级elasticsearch和kibana并开启xpack,并重启关联模块
- import_tasks: addXpack.yml
  tags:
    - addXpack

- block:
  - name: 安装nginx包
    copy:
      src: "server/nginx"
      dest: "/home/<USER>/server/"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755

  - name: 创建部署所需要的目录 
    file:
      path: "/home/<USER>/server/nginx/{{ item }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
      recurse: yes
      state: directory
    with_items:
      - bin
      - config
      - images
      - logs
      - log
      - data
      - tmp
    ignore_errors: True

  - name: load nginx image
    command: "docker load -i /home/<USER>/server/nginx/images/{{ ansible_architecture|lower }}/nginx.tar"
    ignore_errors: True
  
  - name: 配置nginx服务配置文件及脚本
    template:
      src: "nginx/{{ item.key }}"
      dest: "/home/<USER>/server/nginx/{{ item.value }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
    with_items:
      - { key: 'service.sh.j2' , value: 'bin/service.sh' }
      - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
      - { key: 'down.conf.j2' , value: 'data/conf.d/down.conf' }
      - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
    when: ansible_architecture|lower == "x86_64"
    notify:
      - restart nginx

  - name: 配置nginx服务配置文件及脚本
    template:
      src: "nginx/{{ item.key }}"
      dest: "/home/<USER>/server/nginx/{{ item.value }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
    with_items:
      - { key: 'service.sh.j2' , value: 'bin/service.sh' }
      - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
      - { key: 'down_aarch64.conf.j2' , value: 'data/conf.d/down.conf' }
      - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
    when: ansible_architecture|lower == "aarch64"
    notify:
      - restart nginx 

  when:
    - inventory_hostname in groups['nginx'] 

- block:
  - name: copy crash 
    copy:
      src: "server/crash"
      dest: "/home/<USER>/server/"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755

  - name: 创建部署所需要的目录 
    file:
      path: "/home/<USER>/server/crash/{{ item }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
      recurse: yes
      state: directory
    with_items:
      - bin
      - config
      - images
      - log
      - data
      - tmp
    ignore_errors: True

  - name: load crash image
    command: "docker load -i /home/<USER>/server/crash/images/{{ ansible_architecture|lower }}/crash.tar"
    ignore_errors: True

  - name: 配置crash服务配置文件及脚本
    template:
      src: "crash/{{ item.key }}"
      dest: "/home/<USER>/server/crash/{{ item.value }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
    with_items:
      - { key: 'service.sh.j2' , value: 'bin/service.sh' }
      - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
      - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
    notify:
      - restart crash
  when:
    - inventory_hostname in groups['crash'] 

- name: 配置kibana服务配置文件及脚本
  template:
    src: "kibana/{{ item.key }}"
    dest: "/home/<USER>/server/kibana/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  notify:
    - restart kibana
  when:
    - inventory_hostname in groups['kibana'] 

# - block:
#   - name: copy postgres
#     copy:
#       src: "server/postgres"
#       dest: "/home/<USER>/server/"
#       owner: "{{ bangcle_user }}"
#       group: "{{ bangcle_user }}"
#       force: yes
#       mode: 0755

#   - name: 创建部署所需要的目录 
#     file:
#       path: "/home/<USER>/server/postgres/{{ item }}"
#       owner: "{{ bangcle_user }}"
#       group: "{{ bangcle_user }}"
#       force: yes
#       mode: 0755
#       recurse: yes
#       state: directory
#     with_items:
#       - bin
#       - backups
#       - config
#       - images
#       - log
#       - data
#       - tmp
      
#   - name: create user polkitd
#     user:
#       name: polkitd
#       shell: /sbin/nologin
#     when: ansible_distribution.split(' ')[0]|lower == "debian" or ansible_distribution.split(' ')[0]|lower == "uniontech" or ansible_distribution.split(' ')[0]|lower == "ubuntu" or ansible_distribution.split(' ')[0]|lower == "sles"

#   - name: 创建部署所需要的目录 
#     file:
#       path: "/home/<USER>/server/postgres/{{ item }}"
#       owner: polkitd
#       group: "{{ bangcle_user }}"
#       force: yes
#       mode: 0755
#       recurse: yes
#       state: directory
#     with_items:
#       - data

#   - name: load postgres image
#     command: "docker load -i /home/<USER>/server/postgres/images/{{ ansible_architecture|lower }}/postgres.tar"
  
#   - name: 配置postgres服务配置文件及脚本
#     template:
#       src: "postgres/{{ item.key }}"
#       dest: "/home/<USER>/server/postgres/{{ item.value }}"
#       owner: "{{ bangcle_user }}"
#       group: "{{ bangcle_user }}"
#       force: yes
#       mode: 0755
#     with_items:
#       - { key: 'service.sh.j2' , value: 'bin/service.sh' }
#       - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
#       - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
#     notify:
#       - restart postgres
#   when:
#     - inventory_hostname in groups['postgres']

- block:
  - name: copy redis
    copy:
      src: "server/redis"
      dest: "/home/<USER>/server/"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755

  - name: 创建部署所需要的目录 
    file:
      path: "/home/<USER>/server/redis/{{ item }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
      recurse: yes
      state: directory
    with_items:
      - bin
      - config
      - images
      - log
      - data
      - tmp
    ignore_errors: True

  - name: 创建redis的log文件
    command: "touch /home/<USER>/server/redis/log/redis.log"
    ignore_errors: True

  - name: load redis image
    command: "docker load -i /home/<USER>/server/redis/images/{{ ansible_architecture|lower }}/redis.tar"


  - name: 配置redis服务配置文件及脚本
    template:
      src: "redis/{{ item.key }}"
      dest: "/home/<USER>/server/redis/{{ item.value }}"
      owner: "{{ bangcle_user }}"
      group: "{{ bangcle_user }}"
      force: yes
      mode: 0755
    with_items:
      - { key: 'service.sh.j2' , value: 'bin/service.sh' }
      - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
      - { key: 'redis.conf.j2' , value: 'conf/redis.conf' }
      - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
    notify:
      - restart redis
  when:
    - inventory_hostname in groups['redis'] 

- name: 配置zookeeper的配置文件及脚本
  template:
    src: "zookeeper/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'zookeeper/bin/service.sh' }
    - { key: 'startup.sh.j2' , value: 'zookeeper/bin/startup.sh' }
  notify:
    - restart zookeeper 
  when:
    - inventory_hostname in groups['zookeeper'] 

- name: 配置hbase服务配置文件及脚本
  template:
    src: "hbase/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'hbase/bin/service.sh' }
    - { key: 'startup_master.sh.j2' , value: 'hbase/bin/startup_master.sh' }
    - { key: 'startup_regionserver.sh.j2' , value: 'hbase/bin/startup_regionserver.sh' }
  notify:
    - restart hbase
  when:
    - inventory_hostname in groups['hbase'] 

- name: 配置kafka的配置文件及脚本
  template:
    src: "kafka/{{ item.key }}"
    dest: "/home/<USER>/server/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'kafka/bin/service.sh' }
    - { key: 'startup.sh.j2' , value: 'kafka/bin/startup.sh' }
  notify:
    - restart hbase
  when:
    - inventory_hostname in groups['kafka'] 

- name: 映射整体服务配置文件 
  template:
    src: "bangcleUser/{{ item.key }}"
    dest: "/home/<USER>/bin/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service_all.sh.j2', value: 'service_all.sh' }
    - { key: 'service_app_all.sh.j2' , value: 'service_app_all.sh' }
    - { key: 'service_server_all.sh.j2' , value: 'service_server_all.sh' }
    - { key: 'service_check_all.sh.j2' , value: 'service_check_all.sh' }
    - { key: 'service_ops_all.sh.j2' , value: 'service_ops_all.sh' }
    
- name: 重启所有server
  shell: "su -  {{ bangcle_user }} -c \"cd bin; ./service_server_all.sh start\""
  ignore_errors: True

