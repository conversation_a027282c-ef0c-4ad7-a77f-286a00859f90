# HBase ZooKeeper SASL 认证配置指南

## 概述

本文档详细介绍了如何在 HBase 中配置 ZooKeeper SASL（Simple Authentication and Security Layer）认证，以确保 HBase 与 ZooKeeper 之间的安全通信。

## 目录

1. [配置场景](#配置场景)
2. [HBase 管理的 ZooKeeper 配置](#hbase-管理的-zookeeper-配置)
3. [外部 ZooKeeper 配置](#外部-zookeeper-配置)
4. [客户端配置](#客户端配置)
5. [启动和验证](#启动和验证)
6. [故障排除](#故障排除)
7. [参考文档](#参考文档)

## 配置场景

HBase ZooKeeper SASL 认证主要有两种配置场景：

1. **HBase 管理的 ZooKeeper**：HBase 自己启动和管理 ZooKeeper 实例
2. **外部 ZooKeeper**：使用独立部署的 ZooKeeper 集群

## HBase 管理的 ZooKeeper 配置

### 1. hbase-site.xml 配置

```xml
<configuration>
  <property>
    <name>hbase.zookeeper.quorum</name>
    <value>node1.example.com,node2.example.com,node3.example.com</value>
    <description>ZooKeeper 集群节点列表</description>
  </property>
  
  <property>
    <name>hbase.cluster.distributed</name>
    <value>true</value>
    <description>启用分布式模式</description>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.authProvider.1</name>
    <value>org.apache.zookeeper.server.auth.SASLAuthenticationProvider</value>
    <description>启用 SASL 认证提供者</description>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.kerberos.removeHostFromPrincipal</name>
    <value>true</value>
    <description>从 Kerberos 主体中移除主机名</description>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.kerberos.removeRealmFromPrincipal</name>
    <value>true</value>
    <description>从 Kerberos 主体中移除域名</description>
  </property>
</configuration>
```

### 2. JAAS 配置文件

创建 JAAS 配置文件（例如：`hbase-server.jaas`）：

```java
Server {
  com.sun.security.auth.module.Krb5LoginModule required
  useKeyTab=true
  keyTab="/etc/security/keytabs/zookeeper.keytab"
  storeKey=true
  useTicketCache=false
  principal="zookeeper/<EMAIL>";
};

Client {
  com.sun.security.auth.module.Krb5LoginModule required
  useKeyTab=true
  useTicketCache=false
  keyTab="/etc/security/keytabs/hbase.keytab"
  principal="hbase/<EMAIL>";
};
```

### 3. hbase-env.sh 配置

```bash
# 设置客户端 JAAS 配置
export HBASE_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-client.jaas"

# 启用 HBase 管理 ZooKeeper
export HBASE_MANAGES_ZK=true

# 设置服务端 JAAS 配置
export HBASE_ZOOKEEPER_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-server.jaas"
export HBASE_MASTER_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-server.jaas"
export HBASE_REGIONSERVER_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-server.jaas"
```

## 外部 ZooKeeper 配置

### 1. hbase-site.xml 配置

```xml
<configuration>
  <property>
    <name>hbase.zookeeper.quorum</name>
    <value>zk1.example.com,zk2.example.com,zk3.example.com</value>
    <description>外部 ZooKeeper 集群节点</description>
  </property>
  
  <property>
    <name>hbase.cluster.distributed</name>
    <value>true</value>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.authProvider.1</name>
    <value>org.apache.zookeeper.server.auth.SASLAuthenticationProvider</value>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.kerberos.removeHostFromPrincipal</name>
    <value>true</value>
  </property>
  
  <property>
    <name>hbase.zookeeper.property.kerberos.removeRealmFromPrincipal</name>
    <value>true</value>
  </property>
</configuration>
```

### 2. ZooKeeper 服务器 JAAS 配置

在 ZooKeeper 服务器上创建 `zookeeper-server.jaas`：

```java
Server {
  com.sun.security.auth.module.Krb5LoginModule required
  useKeyTab=true
  keyTab="/etc/security/keytabs/zookeeper.keytab"
  storeKey=true
  useTicketCache=false
  principal="zookeeper/<EMAIL>";
};
```

### 3. HBase 客户端 JAAS 配置

创建 `hbase-client.jaas`：

```java
Client {
  com.sun.security.auth.module.Krb5LoginModule required
  useKeyTab=true
  useTicketCache=false
  keyTab="/etc/security/keytabs/hbase.keytab"
  principal="hbase/<EMAIL>";
};
```

### 4. hbase-env.sh 配置

```bash
# 设置客户端 JAAS 配置
export HBASE_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-client.jaas"

# 禁用 HBase 管理 ZooKeeper
export HBASE_MANAGES_ZK=false

# 设置服务端 JAAS 配置
export HBASE_MASTER_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-server.jaas"
export HBASE_REGIONSERVER_OPTS="-Djava.security.auth.login.config=/etc/hbase/conf/hbase-server.jaas"
```

## 客户端配置

### HBase 客户端 hbase-site.xml

```xml
<configuration>
  <property>
    <name>hbase.zookeeper.quorum</name>
    <value>zk1.example.com,zk2.example.com,zk3.example.com</value>
  </property>
  
  <property>
    <name>hbase.security.authentication</name>
    <value>kerberos</value>
  </property>
</configuration>
```

### 客户端 JAAS 配置

```java
Client {
  com.sun.security.auth.module.Krb5LoginModule required
  useKeyTab=false
  useTicketCache=true;
};
```

## 启动和验证

### 1. 启动 ZooKeeper（外部部署时）

```bash
SERVER_JVMFLAGS="-Djava.security.auth.login.config=/etc/zookeeper/conf/zookeeper-server.jaas" \
bin/zkServer.sh start
```

### 2. 启动 HBase 服务

```bash
# 启动 ZooKeeper（如果由 HBase 管理）
bin/hbase-daemons.sh start zookeeper

# 启动 HBase Master
bin/hbase-daemon.sh start master

# 启动 RegionServer
bin/hbase-daemon.sh start regionserver
```

### 3. 验证认证成功

#### ZooKeeper 服务器日志验证

成功的认证日志应包含：

```
INFO zookeeper.Login: successfully logged in.
INFO server.NIOServerCnxnFactory: binding to port 0.0.0.0/0.0.0.0:2181
INFO zookeeper.Login: TGT refresh thread started.
INFO auth.SaslServerCallbackHandler: Successfully authenticated client: 
  authenticationID=hbase/<EMAIL>;
  authorizationID=hbase/<EMAIL>
INFO auth.SaslServerCallbackHandler: Setting authorizedID: hbase
INFO server.ZooKeeperServer: adding SASL authorization for authorizationID: hbase
```

#### HBase 客户端日志验证

```
INFO zookeeper.ZooKeeper: Initiating client connection
INFO zookeeper.Login: successfully logged in.
INFO client.ZooKeeperSaslClient: Client will use GSSAPI as SASL mechanism.
INFO zookeeper.ClientCnxn: Session establishment complete on server
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查防火墙设置
   - 确认 ZooKeeper 端口（默认 2181）是否开放

2. **认证失败**
   - 验证 keytab 文件权限和路径
   - 检查 Kerberos 主体名称是否正确
   - 确认时间同步（Kerberos 对时间敏感）

3. **会话超时**
   - 调整 `zookeeper.session.timeout` 参数
   - 检查网络延迟和 JVM 垃圾回收设置

### 测试连接

使用 HBase shell 测试连接：

```bash
bin/hbase shell
hbase> list
```

## 参考文档

本文档基于以下官方资源整理：

1. **Apache HBase 官方文档 - ZooKeeper 章节**
   - 来源：https://hbase.apache.org/book.html#zk.sasl.auth
   - 版本：Apache HBase 官方文档

2. **主要参考的代码片段来源**：
   - HBase ZooKeeper 配置示例
   - JAAS 配置模板
   - 环境变量设置指南
   - 日志验证示例

3. **相关 JIRA 问题**：
   - HBASE-11349：Thrift 1 代理用户支持
   - HBASE-11474：Thrift 2 代理用户支持
   - HBASE-2611：复制失败场景修复

---

**注意**：本文档中的配置示例需要根据实际环境进行调整，包括主机名、域名、keytab 路径等。在生产环境中部署前，请务必在测试环境中验证配置的正确性。
