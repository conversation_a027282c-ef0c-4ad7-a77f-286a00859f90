# HBase 分布式部署完整指南

## 概述

本文档详细介绍了如何部署和配置 Apache HBase 分布式集群，包括完全分布式模式的配置、集群规划、安装步骤、配置优化和故障排除等内容。

## 目录

1. [部署架构规划](#部署架构规划)
2. [环境准备](#环境准备)
3. [Hadoop 集群配置](#hadoop-集群配置)
4. [HBase 分布式配置](#hbase-分布式配置)
5. [ZooKeeper 集群配置](#zookeeper-集群配置)
6. [集群启动和验证](#集群启动和验证)
7. [性能优化配置](#性能优化配置)
8. [安全配置](#安全配置)
9. [监控和维护](#监控和维护)
10. [故障排除](#故障排除)
11. [参考文档](#参考文档)

## 部署架构规划

### 1. 集群角色规划

典型的 HBase 分布式集群包含以下组件：

- **NameNode**：HDFS 主节点，管理文件系统元数据
- **DataNode**：HDFS 数据节点，存储实际数据
- **HMaster**：HBase 主节点，管理 RegionServer 和表操作
- **RegionServer**：HBase 数据节点，处理读写请求
- **ZooKeeper**：协调服务，管理集群状态

### 1.1 三节点集群的优势

- **成本效益**：相比5节点集群，硬件成本降低40%
- **管理简单**：节点数量少，运维复杂度低
- **资源充分利用**：每个节点承担多个角色，资源利用率高
- **适合场景**：中小型企业、测试环境、开发环境

### 2. 推荐的集群拓扑

```
节点规划示例（3节点集群）：
- node1: NameNode, HMaster, ZooKeeper, DataNode, RegionServer
- node2: DataNode, RegionServer, ZooKeeper, Secondary NameNode
- node3: DataNode, RegionServer, ZooKeeper
```

**角色说明**：
- **node1**：主节点，运行 NameNode 和 HMaster，同时也作为数据节点
- **node2**：数据节点，运行 Secondary NameNode 作为备份
- **node3**：数据节点，纯数据处理角色

### 3. 硬件要求（3节点集群）

- **CPU**：每个节点至少 8 核
- **内存**：
  - node1（主节点）：至少 32GB（运行多个服务）
  - node2/node3（数据节点）：至少 24GB
- **存储**：
  - 系统盘：SSD，至少 100GB
  - 数据盘：2-4块 SATA/SAS 硬盘，每块至少 1TB
- **网络**：千兆以太网

**注意**：3节点集群中 node1 承担更多角色，需要更多资源。

### 4. 三节点集群配置要点

#### 4.1 HDFS 配置调整

```xml
<!-- 3节点集群的副本数设置 -->
<property>
  <name>dfs.replication</name>
  <value>2</value>
  <description>3节点集群建议副本数为2</description>
</property>
```

#### 4.2 HBase 配置调整

```xml
<!-- 3节点集群的 RegionServer 最小启动数 -->
<property>
  <name>hbase.master.wait.on.regionservers.mintostart</name>
  <value>2</value>
  <description>等待至少2个 RegionServer 启动</description>
</property>

<!-- 适合小集群的 Region 大小 -->
<property>
  <name>hbase.hregion.max.filesize</name>
  <value>5368709120</value>
  <description>Region 最大文件大小（5GB，适合小集群）</description>
</property>
```

#### 4.3 资源分配建议

| 节点 | 角色 | 内存分配 | CPU 核心 |
|------|------|----------|----------|
| node1 | NameNode + HMaster + RegionServer + ZK | 24GB | 8核 |
| node2 | DataNode + RegionServer + ZK + Secondary NN | 20GB | 6核 |
| node3 | DataNode + RegionServer + ZK | 20GB | 6核 |

## 环境准备

### 1. 操作系统配置

#### 设置主机名和 hosts 文件

```bash
# 在每个节点上设置主机名
sudo hostnamectl set-hostname node1.example.com

# 配置 /etc/hosts 文件
cat >> /etc/hosts << EOF
************* node1.example.com node1
************* node2.example.com node2
************* node3.example.com node3
EOF
```

#### 配置 SSH 免密登录

```bash
# 在主节点生成密钥对
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 将公钥复制到所有节点
for i in {1..3}; do
  ssh-copy-id node${i}.example.com
done
```

#### 系统参数优化

```bash
# 配置 ulimit 限制
cat >> /etc/security/limits.conf << EOF
hadoop soft nofile 65536
hadoop hard nofile 65536
hadoop soft nproc 32000
hadoop hard nproc 32000
EOF

# 配置内核参数
cat >> /etc/sysctl.conf << EOF
vm.swappiness=1
net.core.somaxconn=1024
net.core.netdev_max_backlog=5000
net.ipv4.tcp_max_syn_backlog=65536
EOF

# 应用配置
sysctl -p
```

### 2. Java 环境安装

```bash
# 安装 OpenJDK 8
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 设置 JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
source ~/.bashrc
```

## Hadoop 集群配置

### 1. 下载和安装 Hadoop

```bash
# 下载 Hadoop
cd /opt
sudo wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz
sudo tar -xzf hadoop-3.3.4.tar.gz
sudo mv hadoop-3.3.4 hadoop
sudo chown -R hadoop:hadoop /opt/hadoop

# 设置环境变量
cat >> ~/.bashrc << EOF
export HADOOP_HOME=/opt/hadoop
export HADOOP_CONF_DIR=\$HADOOP_HOME/etc/hadoop
export PATH=\$PATH:\$HADOOP_HOME/bin:\$HADOOP_HOME/sbin
EOF
source ~/.bashrc
```

### 2. 配置 Hadoop

#### core-site.xml

```xml
<configuration>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://node1.example.com:9000</value>
  </property>

  <property>
    <name>hadoop.tmp.dir</name>
    <value>/data/hadoop/tmp</value>
  </property>

  <property>
    <name>io.file.buffer.size</name>
    <value>131072</value>
  </property>
</configuration>
```

#### hdfs-site.xml

```xml
<configuration>
  <property>
    <name>dfs.namenode.name.dir</name>
    <value>/data/hadoop/namenode</value>
  </property>

  <property>
    <name>dfs.datanode.data.dir</name>
    <value>/data/hadoop/datanode</value>
  </property>

  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>

  <property>
    <name>dfs.blocksize</name>
    <value>134217728</value>
  </property>

  <property>
    <name>dfs.namenode.handler.count</name>
    <value>100</value>
  </property>
</configuration>
```

#### workers 文件

```
node1.example.com
node2.example.com
node3.example.com
```

**注意**：3节点集群中，所有节点都作为 DataNode 运行。

### 3. 启动 Hadoop 集群

```bash
# 格式化 NameNode（仅首次）
hdfs namenode -format

# 启动 HDFS
start-dfs.sh

# 验证 HDFS 状态
hdfs dfsadmin -report
```

## HBase 分布式配置

### 1. 下载和安装 HBase

```bash
# 下载 HBase
cd /opt
sudo wget https://archive.apache.org/dist/hbase/2.5.4/hbase-2.5.4-bin.tar.gz
sudo tar -xzf hbase-2.5.4-bin.tar.gz
sudo mv hbase-2.5.4 hbase
sudo chown -R hadoop:hadoop /opt/hbase

# 设置环境变量
cat >> ~/.bashrc << EOF
export HBASE_HOME=/opt/hbase
export PATH=\$PATH:\$HBASE_HOME/bin
EOF
source ~/.bashrc
```

### 2. 配置 HBase

#### hbase-site.xml（完全分布式配置）

```xml
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
  <!-- 基础配置 -->
  <property>
    <name>hbase.cluster.distributed</name>
    <value>true</value>
    <description>启用分布式模式</description>
  </property>

  <property>
    <name>hbase.rootdir</name>
    <value>hdfs://node1.example.com:9000/hbase</value>
    <description>HBase 在 HDFS 中的根目录</description>
  </property>

  <!-- ZooKeeper 配置 -->
  <property>
    <name>hbase.zookeeper.quorum</name>
    <value>node1.example.com,node2.example.com,node3.example.com</value>
    <description>ZooKeeper 集群节点</description>
  </property>

  <property>
    <name>hbase.zookeeper.property.clientPort</name>
    <value>2181</value>
    <description>ZooKeeper 客户端端口</description>
  </property>

  <property>
    <name>hbase.zookeeper.property.dataDir</name>
    <value>/data/zookeeper</value>
    <description>ZooKeeper 数据目录</description>
  </property>

  <!-- 性能优化配置 -->
  <property>
    <name>hbase.regionserver.handler.count</name>
    <value>30</value>
    <description>RegionServer RPC 处理线程数</description>
  </property>

  <property>
    <name>hbase.master.wait.on.regionservers.mintostart</name>
    <value>2</value>
    <description>Master 启动前等待的最少 RegionServer 数量（3节点集群建议2个）</description>
  </property>

  <property>
    <name>hbase.hregion.memstore.flush.size</name>
    <value>134217728</value>
    <description>MemStore 刷写阈值（128MB）</description>
  </property>

  <property>
    <name>hbase.hregion.max.filesize</name>
    <value>10737418240</value>
    <description>Region 最大文件大小（10GB）</description>
  </property>
</configuration>
```

#### regionservers 文件

```
node1.example.com
node2.example.com
node3.example.com
```

**说明**：3节点集群中，所有节点都运行 RegionServer。

#### backup-masters 文件（可选）

```
node2.example.com
```

**说明**：node2 作为备用 Master，提供高可用性。

#### hbase-env.sh 配置

```bash
# Java 环境
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk

# HBase 管理 ZooKeeper
export HBASE_MANAGES_ZK=true

# JVM 参数优化
export HBASE_HEAPSIZE=8G
export HBASE_OFFHEAPSIZE=8G

# Master JVM 参数（3节点集群调整）
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS -Xms2g -Xmx2g -XX:+UseG1GC"

# RegionServer JVM 参数（3节点集群调整）
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -Xms8g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# ZooKeeper JVM 参数
export HBASE_ZOOKEEPER_OPTS="$HBASE_ZOOKEEPER_OPTS -Xms512m -Xmx512m"
```

## ZooKeeper 集群配置

### 1. ZooKeeper 配置文件

#### zoo.cfg

```bash
# 基础配置
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/data/zookeeper
clientPort=2181

# 集群配置
server.1=node1.example.com:2888:3888
server.2=node2.example.com:2888:3888
server.3=node3.example.com:2888:3888

# 性能优化
maxClientCnxns=300
autopurge.snapRetainCount=3
autopurge.purgeInterval=1
```

#### myid 文件

在每个 ZooKeeper 节点的数据目录创建 myid 文件：

```bash
# node1
echo "1" > /data/zookeeper/myid

# node2
echo "2" > /data/zookeeper/myid

# node3
echo "3" > /data/zookeeper/myid
```

### 2. 启动 ZooKeeper 集群

```bash
# 在每个 ZooKeeper 节点上启动
zkServer.sh start

# 检查状态
zkServer.sh status
```

## 集群启动和验证

### 1. 启动顺序

```bash
# 1. 启动 Hadoop 集群
start-dfs.sh

# 2. 启动 ZooKeeper 集群（如果使用外部 ZooKeeper）
# 在每个 ZooKeeper 节点执行
zkServer.sh start

# 3. 启动 HBase 集群
start-hbase.sh
```

### 2. 验证集群状态

#### 检查 HDFS 状态

```bash
hdfs dfsadmin -report
hdfs dfs -ls /
```

#### 检查 ZooKeeper 状态

```bash
# 检查 ZooKeeper 集群状态
for i in {1..3}; do
  echo "=== node${i} ZooKeeper Status ==="
  ssh node${i}.example.com "zkServer.sh status"
done
```

#### 检查 HBase 状态

```bash
# 使用 HBase shell
hbase shell
hbase> status
hbase> list

# 检查 Web UI
# Master: http://node1.example.com:16010
# RegionServer: http://node2.example.com:16030
```

### 3. 验证进程运行

```bash
# 在 Master 节点检查进程
jps
# 应该看到：HMaster, HQuorumPeer

# 在 RegionServer 节点检查进程
jps
# 应该看到：HRegionServer, HQuorumPeer, DataNode
```

## 性能优化配置

### 1. JVM 参数优化

#### RegionServer JVM 优化（3节点集群）

```bash
# 在 hbase-env.sh 中配置（适合3节点集群的内存配置）
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS \
  -Xms12g -Xmx12g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:G1HeapRegionSize=16m \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseCGroupMemoryLimitForHeap \
  -XX:+PrintGC \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/hbase/gc-regionserver.log"
```

#### Master JVM 优化（3节点集群）

```bash
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS \
  -Xms4g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200"
```

### 2. HBase 配置优化

#### 内存和缓存优化

```xml
<!-- MemStore 配置 -->
<property>
  <name>hbase.regionserver.global.memstore.size</name>
  <value>0.4</value>
  <description>RegionServer 总内存的 40% 用于 MemStore</description>
</property>

<property>
  <name>hbase.regionserver.global.memstore.size.lower.limit</name>
  <value>0.38</value>
  <description>MemStore 下限阈值</description>
</property>

<!-- BlockCache 配置 -->
<property>
  <name>hfile.block.cache.size</name>
  <value>0.4</value>
  <description>RegionServer 总内存的 40% 用于 BlockCache</description>
</property>

<property>
  <name>hbase.bucketcache.ioengine</name>
  <value>offheap</value>
  <description>使用堆外内存作为二级缓存</description>
</property>

<property>
  <name>hbase.bucketcache.size</name>
  <value>16384</value>
  <description>BucketCache 大小（MB）</description>
</property>
```

#### 压缩和编码优化

```xml
<!-- 数据压缩 -->
<property>
  <name>hbase.hregion.memstore.flush.size</name>
  <value>134217728</value>
  <description>MemStore 刷写大小（128MB）</description>
</property>

<property>
  <name>hbase.hstore.compaction.min</name>
  <value>3</value>
  <description>最小压缩文件数</description>
</property>

<property>
  <name>hbase.hstore.compaction.max</name>
  <value>10</value>
  <description>最大压缩文件数</description>
</property>

<property>
  <name>hbase.hstore.compaction.max.size</name>
  <value>10737418240</value>
  <description>压缩文件最大大小（10GB）</description>
</property>
```

### 3. 网络和 RPC 优化

```xml
<!-- RPC 处理优化（3节点集群调整） -->
<property>
  <name>hbase.regionserver.handler.count</name>
  <value>50</value>
  <description>RegionServer RPC 处理线程数（3节点集群适中配置）</description>
</property>

<property>
  <name>hbase.ipc.server.callqueue.handler.factor</name>
  <value>0.1</value>
  <description>RPC 队列因子</description>
</property>

<property>
  <name>hbase.ipc.server.callqueue.read.ratio</name>
  <value>0.7</value>
  <description>读写队列比例</description>
</property>

<!-- 网络优化 -->
<property>
  <name>hbase.rpc.timeout</name>
  <value>60000</value>
  <description>RPC 超时时间（毫秒）</description>
</property>

<property>
  <name>hbase.client.scanner.timeout.period</name>
  <value>60000</value>
  <description>Scanner 超时时间（毫秒）</description>
</property>
```

## 安全配置

### 1. 基础安全配置

#### 启用 Kerberos 认证

```xml
<property>
  <name>hbase.security.authentication</name>
  <value>kerberos</value>
  <description>启用 Kerberos 认证</description>
</property>

<property>
  <name>hbase.security.authorization</name>
  <value>true</value>
  <description>启用授权</description>
</property>

<property>
  <name>hbase.master.kerberos.principal</name>
  <value>hbase/<EMAIL></value>
  <description>Master Kerberos 主体</description>
</property>

<property>
  <name>hbase.master.keytab.file</name>
  <value>/etc/security/keytabs/hbase.keytab</value>
  <description>Master keytab 文件路径</description>
</property>

<property>
  <name>hbase.regionserver.kerberos.principal</name>
  <value>hbase/<EMAIL></value>
  <description>RegionServer Kerberos 主体</description>
</property>

<property>
  <name>hbase.regionserver.keytab.file</name>
  <value>/etc/security/keytabs/hbase.keytab</value>
  <description>RegionServer keytab 文件路径</description>
</property>
```

### 2. Web UI 安全配置

```xml
<property>
  <name>hbase.security.authentication.ui</name>
  <value>kerberos</value>
  <description>Web UI 认证方式</description>
</property>

<property>
  <name>hbase.security.authentication.spnego.kerberos.principal</name>
  <value>HTTP/<EMAIL></value>
  <description>SPNEGO Kerberos 主体</description>
</property>

<property>
  <name>hbase.security.authentication.spnego.kerberos.keytab</name>
  <value>/etc/security/keytabs/spnego.keytab</value>
  <description>SPNEGO keytab 文件路径</description>
</property>
```

## 监控和维护

### 1. 日志配置

#### 配置日志级别

```xml
<!-- 在 log4j.properties 中配置 -->
log4j.logger.org.apache.hadoop.hbase=INFO
log4j.logger.org.apache.zookeeper=WARN
log4j.logger.org.apache.hadoop.hdfs=WARN

# GC 日志配置
log4j.logger.org.apache.hadoop.hbase.regionserver.wal=DEBUG
```

### 2. 监控指标

#### JMX 监控配置

```bash
# 在 hbase-env.sh 中启用 JMX
export HBASE_JMX_BASE="-Dcom.sun.management.jmxremote \
  -Dcom.sun.management.jmxremote.port=10101 \
  -Dcom.sun.management.jmxremote.authenticate=false \
  -Dcom.sun.management.jmxremote.ssl=false"

export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS $HBASE_JMX_BASE"
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS $HBASE_JMX_BASE"
```

#### 关键监控指标

- **RegionServer 指标**：
  - 请求数量和延迟
  - MemStore 大小和刷写频率
  - BlockCache 命中率
  - Compaction 队列长度

- **Master 指标**：
  - Region 分配时间
  - 负载均衡操作
  - 集群负载情况

- **系统指标**：
  - CPU 使用率
  - 内存使用率
  - 磁盘 I/O
  - 网络流量

### 3. 备份策略

#### 数据备份

```bash
# 使用 HBase 快照功能
hbase shell
hbase> snapshot 'table_name', 'snapshot_name'

# 导出快照到其他集群
hbase org.apache.hadoop.hbase.snapshot.ExportSnapshot \
  -snapshot snapshot_name \
  -copy-to hdfs://backup-cluster:9000/hbase
```
```
## 集群启动和验证

### 1. 启动顺序
```bash
# 1. 启动 Hadoop 集群
start-dfs.sh

# 2. 启动 ZooKeeper 集群（如果使用外部 ZooKeeper）
# 在每个 ZooKeeper 节点执行
zkServer.sh start

# 3. 启动 HBase 集群
start-hbase.sh
```
### 2. 验证集群状态

#### 检查 HDFS 状态
```bash
hdfs dfsadmin -report
hdfs dfs -ls /
```
#### 检查 ZooKeeper 状态
```bash
# 检查 ZooKeeper 集群状态
for i in {1..3}; do
  echo "=== node${i} ZooKeeper Status ==="
  ssh node${i}.example.com "zkServer.sh status"
done
```
#### 检查 HBase 状态
```bash
# 使用 HBase shell
hbase shell
hbase> status
hbase> list

# 检查 Web UI
# Master: http://node1.example.com:16010
# RegionServer: http://node2.example.com:16030
```
### 3. 验证进程运行
```bash
# 在 Master 节点检查进程
jps
# 应该看到：HMaster, HQuorumPeer

# 在 RegionServer 节点检查进程
jps
# 应该看到：HRegionServer, HQuorumPeer, DataNode
```

## 性能优化配置

### 1. JVM 参数优化

#### RegionServer JVM 优化

```bash
# 在 hbase-env.sh 中配置
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS \
  -Xms32g -Xmx32g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:G1HeapRegionSize=32m \
  -XX:+UnlockExperimentalVMOptions \
  -XX:+UseCGroupMemoryLimitForHeap \
  -XX:+PrintGC \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/hbase/gc-regionserver.log"
```

#### Master JVM 优化

```bash
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS \
  -Xms8g -Xmx8g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200"
```

### 2. HBase 配置优化

#### 内存和缓存优化

```xml
<!-- MemStore 配置 -->
<property>
  <name>hbase.regionserver.global.memstore.size</name>
  <value>0.4</value>
  <description>RegionServer 总内存的 40% 用于 MemStore</description>
</property>

<property>
  <name>hbase.regionserver.global.memstore.size.lower.limit</name>
  <value>0.38</value>
  <description>MemStore 下限阈值</description>
</property>

<!-- BlockCache 配置 -->
<property>
  <name>hfile.block.cache.size</name>
  <value>0.4</value>
  <description>RegionServer 总内存的 40% 用于 BlockCache</description>
</property>

<property>
  <name>hbase.bucketcache.ioengine</name>
  <value>offheap</value>
  <description>使用堆外内存作为二级缓存</description>
</property>

<property>
  <name>hbase.bucketcache.size</name>
  <value>16384</value>
  <description>BucketCache 大小（MB）</description>
</property>
```

#### 压缩和编码优化

```xml
<!-- 数据压缩 -->
<property>
  <name>hbase.hregion.memstore.flush.size</name>
  <value>134217728</value>
  <description>MemStore 刷写大小（128MB）</description>
</property>

<property>
  <name>hbase.hstore.compaction.min</name>
  <value>3</value>
  <description>最小压缩文件数</description>
</property>

<property>
  <name>hbase.hstore.compaction.max</name>
  <value>10</value>
  <description>最大压缩文件数</description>
</property>

<property>
  <name>hbase.hstore.compaction.max.size</name>
  <value>10737418240</value>
  <description>压缩文件最大大小（10GB）</description>
</property>
```
zkServer.sh start

# 检查状态
zkServer.sh status
```

## 集群启动和验证

### 1. 启动顺序

```bash
# 1. 启动 Hadoop 集群
start-dfs.sh

# 2. 启动 ZooKeeper 集群（如果使用外部 ZooKeeper）
# 在每个 ZooKeeper 节点执行
zkServer.sh start

# 3. 启动 HBase 集群
start-hbase.sh
```

### 2. 验证集群状态

#### 检查 HDFS 状态

```bash
hdfs dfsadmin -report
hdfs dfs -ls /
```

#### 检查 ZooKeeper 状态

```bash
# 检查 ZooKeeper 集群状态
for i in {1..3}; do
  echo "=== node${i} ZooKeeper Status ==="
  ssh node${i}.example.com "zkServer.sh status"
done
```

#### 检查 HBase 状态

```bash
# 使用 HBase shell
hbase shell
hbase> status
hbase> list

# 检查 Web UI
# Master: http://node1.example.com:16010
# RegionServer: http://node2.example.com:16030
```

### 3. 验证进程运行

```bash
# 在 Master 节点检查进程
jps
# 应该看到：HMaster, HQuorumPeer

# 在 RegionServer 节点检查进程
jps
# 应该看到：HRegionServer, HQuorumPeer, DataNode
```

## 故障排除

### 1. 常见启动问题

#### HBase Master 无法启动

**症状**：Master 进程启动后立即退出

**可能原因和解决方案**：

```bash
# 检查 Master 日志
tail -f $HBASE_HOME/logs/hbase-*-master-*.log

# 常见问题：
# 1. HDFS 连接问题
hdfs dfs -ls /  # 验证 HDFS 连接

# 2. ZooKeeper 连接问题
zkCli.sh -server node1.example.com:2181
ls /

# 3. 权限问题
hdfs dfs -chown -R hbase:hbase /hbase
```

#### RegionServer 无法启动

**症状**：RegionServer 进程无法启动或频繁重启

**解决方案**：

```bash
# 检查 RegionServer 日志
tail -f $HBASE_HOME/logs/hbase-*-regionserver-*.log

# 检查内存配置
free -h
# 确保有足够内存分配给 RegionServer

# 检查端口占用
netstat -tulpn | grep 16020
```

### 2. 性能问题排查

#### 读写延迟高

**排查步骤**：

```bash
# 1. 检查 RegionServer 负载
hbase shell
hbase> status 'detailed'

# 2. 检查 Compaction 状态
hbase> compact 'table_name'

# 3. 检查 GC 日志
tail -f /var/log/hbase/gc-regionserver.log

# 4. 检查系统资源
iostat -x 1
top -p $(pgrep -f HRegionServer)
```

#### Region 热点问题

**解决方案**：

```bash
# 1. 检查 Region 分布
hbase shell
hbase> status 'detailed'

# 2. 手动分割热点 Region
hbase> split 'table_name', 'split_key'

# 3. 启用负载均衡
hbase> balance_switch true
hbase> balancer
```

### 3. 数据一致性问题

#### Region 不一致

**修复步骤**：

```bash
# 1. 运行 HBCK 检查
hbase hbck

# 2. 修复不一致的 Region
hbase hbck -fixAssignments

# 3. 修复 Meta 表问题
hbase hbck -fixMeta

# 4. 修复 HDFS 引用问题
hbase hbck -fixHdfsHoles -fixHdfsOrphans
```

### 4. 集群维护

#### 滚动重启

```bash
# 1. 重启 RegionServer（逐个进行，3节点集群）
for server in node2 node3; do
  echo "Restarting RegionServer on $server"
  ssh $server "$HBASE_HOME/bin/hbase-daemon.sh stop regionserver"
  sleep 30
  ssh $server "$HBASE_HOME/bin/hbase-daemon.sh start regionserver"
  sleep 60
done

# 最后重启 node1（主节点）
echo "Restarting RegionServer on node1"
$HBASE_HOME/bin/hbase-daemon.sh stop regionserver
sleep 30
$HBASE_HOME/bin/hbase-daemon.sh start regionserver

# 2. 重启 Master
$HBASE_HOME/bin/hbase-daemon.sh stop master
sleep 30
$HBASE_HOME/bin/hbase-daemon.sh start master
```

#### 添加新节点

```bash
# 1. 在新节点上安装和配置 HBase
# 2. 更新 regionservers 文件（示例：添加第4个节点）
echo "node4.example.com" >> $HBASE_HOME/conf/regionservers

# 3. 启动新的 RegionServer
ssh node4.example.com "$HBASE_HOME/bin/hbase-daemon.sh start regionserver"

# 4. 触发负载均衡
hbase shell
hbase> balancer
```

### 5. 监控和告警

#### 关键监控脚本

```bash
#!/bin/bash
# hbase_health_check.sh

# 检查 HBase 服务状态
check_hbase_status() {
    echo "=== HBase Cluster Status ==="
    echo "status" | hbase shell -n 2>/dev/null | grep -E "(servers|dead)"
}

# 检查 Region 数量
check_region_count() {
    echo "=== Region Count ==="
    echo "status 'detailed'" | hbase shell -n 2>/dev/null | grep -E "regions="
}

# 检查 HDFS 使用率
check_hdfs_usage() {
    echo "=== HDFS Usage ==="
    hdfs dfs -df /hbase
}

# 执行检查
check_hbase_status
check_region_count
check_hdfs_usage
```

## 参考文档

本文档基于以下官方资源整理：

### 1. 官方文档来源

- **Apache HBase 官方文档**
  - 来源：https://hbase.apache.org/book.html
  - 章节：Configuration, Getting Started, Architecture
  - 版本：Apache HBase 2.5.x

- **Apache Hadoop 官方文档**
  - 来源：https://hadoop.apache.org/docs/stable/
  - 章节：HDFS Configuration, Cluster Setup

- **Apache ZooKeeper 官方文档**
  - 来源：https://zookeeper.apache.org/doc/current/
  - 章节：Administrator's Guide, Programmer's Guide

### 2. 主要参考的配置示例

- **分布式集群配置**：
  - hbase-site.xml 完整配置示例
  - regionservers 文件配置
  - 环境变量设置指南

- **性能优化配置**：
  - JVM 参数优化示例
  - 内存和缓存配置
  - 网络和 RPC 优化

- **安全配置**：
  - Kerberos 认证配置
  - Web UI 安全设置
  - 权限管理配置

### 3. 相关技术文档

- **JIRA 问题参考**：
  - HBASE-6389：Master 等待 RegionServer 启动优化
  - HBASE-22749：分布式 MOB 压缩
  - HBASE-18095：无 ZooKeeper 客户端连接

- **最佳实践指南**：
  - HBase 集群规划和容量评估
  - 数据模型设计最佳实践
  - 运维监控和故障处理

### 4. 社区资源

- **Apache HBase 邮件列表**：
  - 用户邮件列表：<EMAIL>
  - 开发者邮件列表：<EMAIL>

- **官方 Wiki**：
  - https://cwiki.apache.org/confluence/display/HBASE/

---

**重要提醒**：

1. **环境适配**：本文档中的所有配置示例需要根据实际环境进行调整，包括主机名、IP 地址、路径等。

2. **版本兼容性**：配置参数可能因 HBase 版本而异，请参考对应版本的官方文档。

3. **测试验证**：在生产环境部署前，请务必在测试环境中完整验证所有配置。

4. **安全考虑**：生产环境建议启用安全认证和授权机制。

5. **监控告警**：建议建立完善的监控和告警体系，及时发现和处理问题。

---

## 附录：三节点集群部署检查清单

### A. 部署前检查

- [ ] **硬件资源**：
  - [ ] node1: 32GB内存, 8核CPU
  - [ ] node2: 24GB内存, 6核CPU
  - [ ] node3: 24GB内存, 6核CPU
  - [ ] 每个节点至少100GB系统盘 + 2TB数据盘

- [ ] **网络配置**：
  - [ ] 所有节点网络互通
  - [ ] hosts文件配置正确
  - [ ] SSH免密登录配置完成

- [ ] **系统配置**：
  - [ ] ulimit参数设置
  - [ ] 内核参数优化
  - [ ] Java环境安装

### B. 服务配置检查

- [ ] **Hadoop配置**：
  - [ ] core-site.xml: fs.defaultFS指向node1
  - [ ] hdfs-site.xml: 副本数设置为2
  - [ ] workers文件包含3个节点

- [ ] **HBase配置**：
  - [ ] hbase-site.xml: 分布式模式启用
  - [ ] regionservers文件包含3个节点
  - [ ] backup-masters配置node2
  - [ ] JVM参数适合3节点集群

- [ ] **ZooKeeper配置**：
  - [ ] zoo.cfg包含3个server配置
  - [ ] 每个节点myid文件正确
  - [ ] 数据目录权限正确

### C. 启动验证检查

- [ ] **HDFS验证**：
  - [ ] NameNode启动正常
  - [ ] 3个DataNode都已注册
  - [ ] `hdfs dfsadmin -report`显示正常

- [ ] **ZooKeeper验证**：
  - [ ] 3个ZooKeeper节点都启动
  - [ ] 有1个leader，2个follower
  - [ ] `zkServer.sh status`显示正常

- [ ] **HBase验证**：
  - [ ] HMaster启动正常
  - [ ] 3个RegionServer都已注册
  - [ ] `hbase shell`可以正常连接
  - [ ] Web UI可以访问

### D. 性能验证检查

- [ ] **基础功能测试**：
  - [ ] 创建测试表成功
  - [ ] 数据写入正常
  - [ ] 数据读取正常
  - [ ] Region分割正常

- [ ] **负载均衡测试**：
  - [ ] Region分布均匀
  - [ ] 负载均衡器工作正常
  - [ ] 故障转移测试通过

### E. 监控配置检查

- [ ] **日志配置**：
  - [ ] 日志级别设置合理
  - [ ] 日志轮转配置
  - [ ] GC日志启用

- [ ] **监控指标**：
  - [ ] JMX监控启用
  - [ ] 关键指标收集正常
  - [ ] 告警规则配置

### F. 三节点集群特殊注意事项

⚠️ **重要提醒**：

1. **数据安全**：3节点集群容错能力有限，建议：
   - 定期备份重要数据
   - 监控磁盘健康状态
   - 准备快速扩容方案

2. **性能考虑**：
   - node1承担多个角色，需重点监控
   - 网络带宽可能成为瓶颈
   - 适当调整并发参数

3. **扩容规划**：
   - 预留扩容到5节点的方案
   - 考虑角色分离的升级路径
   - 准备数据迁移计划

4. **故障处理**：
   - 任何节点故障都可能影响服务
   - 准备快速恢复预案
   - 建立完善的监控告警
