# HBase 分布式部署完整指南

## 概述

本文档详细介绍了如何部署和配置 Apache HBase 分布式集群，包括完全分布式模式的配置、集群规划、安装步骤、配置优化和故障排除等内容。

## 目录

1. [部署架构规划](#部署架构规划)
2. [环境准备](#环境准备)
3. [Hadoop 集群配置](#hadoop-集群配置)
4. [HBase 分布式配置](#hbase-分布式配置)
5. [ZooKeeper 集群配置](#zookeeper-集群配置)
6. [集群启动和验证](#集群启动和验证)
7. [性能优化配置](#性能优化配置)
8. [安全配置](#安全配置)
9. [监控和维护](#监控和维护)
10. [故障排除](#故障排除)
11. [参考文档](#参考文档)

## 部署架构规划

### 1. 集群角色规划

典型的 HBase 分布式集群包含以下组件：

- **NameNode**：HDFS 主节点，管理文件系统元数据
- **DataNode**：HDFS 数据节点，存储实际数据
- **HMaster**：HBase 主节点，管理 RegionServer 和表操作
- **RegionServer**：HBase 数据节点，处理读写请求
- **ZooKeeper**：协调服务，管理集群状态

### 2. 推荐的集群拓扑

```
节点规划示例（5节点集群）：
- node1: NameNode, HMaster, ZooKeeper
- node2: DataNode, RegionServer, ZooKeeper
- node3: DataNode, RegionServer, ZooKeeper
- node4: DataNode, RegionServer
- node5: DataNode, RegionServer, Secondary NameNode
```

### 3. 硬件要求

- **CPU**：每个节点至少 8 核
- **内存**：
  - NameNode/HMaster：至少 16GB
  - RegionServer：至少 32GB
  - ZooKeeper：至少 4GB
- **存储**：
  - 系统盘：SSD，至少 100GB
  - 数据盘：多块 SATA/SAS 硬盘
- **网络**：千兆以太网

## 环境准备

### 1. 操作系统配置

#### 设置主机名和 hosts 文件

```bash
# 在每个节点上设置主机名
sudo hostnamectl set-hostname node1.example.com

# 配置 /etc/hosts 文件
cat >> /etc/hosts << EOF
************* node1.example.com node1
************* node2.example.com node2
************* node3.example.com node3
************* node4.example.com node4
************* node5.example.com node5
EOF
```

#### 配置 SSH 免密登录

```bash
# 在主节点生成密钥对
ssh-keygen -t rsa -P '' -f ~/.ssh/id_rsa

# 将公钥复制到所有节点
for i in {1..5}; do
  ssh-copy-id node${i}.example.com
done
```

#### 系统参数优化

```bash
# 配置 ulimit 限制
cat >> /etc/security/limits.conf << EOF
hadoop soft nofile 65536
hadoop hard nofile 65536
hadoop soft nproc 32000
hadoop hard nproc 32000
EOF

# 配置内核参数
cat >> /etc/sysctl.conf << EOF
vm.swappiness=1
net.core.somaxconn=1024
net.core.netdev_max_backlog=5000
net.ipv4.tcp_max_syn_backlog=65536
EOF

# 应用配置
sysctl -p
```

### 2. Java 环境安装

```bash
# 安装 OpenJDK 8
sudo yum install -y java-1.8.0-openjdk java-1.8.0-openjdk-devel

# 设置 JAVA_HOME
echo 'export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk' >> ~/.bashrc
source ~/.bashrc
```

## Hadoop 集群配置

### 1. 下载和安装 Hadoop

```bash
# 下载 Hadoop
cd /opt
sudo wget https://archive.apache.org/dist/hadoop/common/hadoop-3.3.4/hadoop-3.3.4.tar.gz
sudo tar -xzf hadoop-3.3.4.tar.gz
sudo mv hadoop-3.3.4 hadoop
sudo chown -R hadoop:hadoop /opt/hadoop

# 设置环境变量
cat >> ~/.bashrc << EOF
export HADOOP_HOME=/opt/hadoop
export HADOOP_CONF_DIR=\$HADOOP_HOME/etc/hadoop
export PATH=\$PATH:\$HADOOP_HOME/bin:\$HADOOP_HOME/sbin
EOF
source ~/.bashrc
```

### 2. 配置 Hadoop

#### core-site.xml

```xml
<configuration>
  <property>
    <name>fs.defaultFS</name>
    <value>hdfs://node1.example.com:9000</value>
  </property>

  <property>
    <name>hadoop.tmp.dir</name>
    <value>/data/hadoop/tmp</value>
  </property>

  <property>
    <name>io.file.buffer.size</name>
    <value>131072</value>
  </property>
</configuration>
```

#### hdfs-site.xml

```xml
<configuration>
  <property>
    <name>dfs.namenode.name.dir</name>
    <value>/data/hadoop/namenode</value>
  </property>

  <property>
    <name>dfs.datanode.data.dir</name>
    <value>/data/hadoop/datanode</value>
  </property>

  <property>
    <name>dfs.replication</name>
    <value>3</value>
  </property>

  <property>
    <name>dfs.blocksize</name>
    <value>134217728</value>
  </property>

  <property>
    <name>dfs.namenode.handler.count</name>
    <value>100</value>
  </property>
</configuration>
```

#### workers 文件

```
node2.example.com
node3.example.com
node4.example.com
node5.example.com
```

### 3. 启动 Hadoop 集群

```bash
# 格式化 NameNode（仅首次）
hdfs namenode -format

# 启动 HDFS
start-dfs.sh

# 验证 HDFS 状态
hdfs dfsadmin -report
```

## HBase 分布式配置

### 1. 下载和安装 HBase

```bash
# 下载 HBase
cd /opt
sudo wget https://archive.apache.org/dist/hbase/2.5.4/hbase-2.5.4-bin.tar.gz
sudo tar -xzf hbase-2.5.4-bin.tar.gz
sudo mv hbase-2.5.4 hbase
sudo chown -R hadoop:hadoop /opt/hbase

# 设置环境变量
cat >> ~/.bashrc << EOF
export HBASE_HOME=/opt/hbase
export PATH=\$PATH:\$HBASE_HOME/bin
EOF
source ~/.bashrc
```

### 2. 配置 HBase

#### hbase-site.xml（完全分布式配置）

```xml
<?xml version="1.0"?>
<?xml-stylesheet type="text/xsl" href="configuration.xsl"?>
<configuration>
  <!-- 基础配置 -->
  <property>
    <name>hbase.cluster.distributed</name>
    <value>true</value>
    <description>启用分布式模式</description>
  </property>

  <property>
    <name>hbase.rootdir</name>
    <value>hdfs://node1.example.com:9000/hbase</value>
    <description>HBase 在 HDFS 中的根目录</description>
  </property>

  <!-- ZooKeeper 配置 -->
  <property>
    <name>hbase.zookeeper.quorum</name>
    <value>node1.example.com,node2.example.com,node3.example.com</value>
    <description>ZooKeeper 集群节点</description>
  </property>

  <property>
    <name>hbase.zookeeper.property.clientPort</name>
    <value>2181</value>
    <description>ZooKeeper 客户端端口</description>
  </property>

  <property>
    <name>hbase.zookeeper.property.dataDir</name>
    <value>/data/zookeeper</value>
    <description>ZooKeeper 数据目录</description>
  </property>

  <!-- 性能优化配置 -->
  <property>
    <name>hbase.regionserver.handler.count</name>
    <value>30</value>
    <description>RegionServer RPC 处理线程数</description>
  </property>

  <property>
    <name>hbase.master.wait.on.regionservers.mintostart</name>
    <value>3</value>
    <description>Master 启动前等待的最少 RegionServer 数量</description>
  </property>

  <property>
    <name>hbase.hregion.memstore.flush.size</name>
    <value>134217728</value>
    <description>MemStore 刷写阈值（128MB）</description>
  </property>

  <property>
    <name>hbase.hregion.max.filesize</name>
    <value>10737418240</value>
    <description>Region 最大文件大小（10GB）</description>
  </property>
</configuration>
```

#### regionservers 文件

```
node2.example.com
node3.example.com
node4.example.com
node5.example.com
```

#### backup-masters 文件（可选）

```
node2.example.com
```

#### hbase-env.sh 配置

```bash
# Java 环境
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk

# HBase 管理 ZooKeeper
export HBASE_MANAGES_ZK=true

# JVM 参数优化
export HBASE_HEAPSIZE=8G
export HBASE_OFFHEAPSIZE=8G

# Master JVM 参数
export HBASE_MASTER_OPTS="$HBASE_MASTER_OPTS -Xms4g -Xmx4g -XX:+UseG1GC"

# RegionServer JVM 参数
export HBASE_REGIONSERVER_OPTS="$HBASE_REGIONSERVER_OPTS -Xms16g -Xmx16g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# ZooKeeper JVM 参数
export HBASE_ZOOKEEPER_OPTS="$HBASE_ZOOKEEPER_OPTS -Xms1g -Xmx1g"
```

## ZooKeeper 集群配置

### 1. ZooKeeper 配置文件

#### zoo.cfg

```bash
# 基础配置
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/data/zookeeper
clientPort=2181

# 集群配置
server.1=node1.example.com:2888:3888
server.2=node2.example.com:2888:3888
server.3=node3.example.com:2888:3888

# 性能优化
maxClientCnxns=300
autopurge.snapRetainCount=3
autopurge.purgeInterval=1
```

#### myid 文件

在每个 ZooKeeper 节点的数据目录创建 myid 文件：

```bash
# node1
echo "1" > /data/zookeeper/myid

# node2
echo "2" > /data/zookeeper/myid

# node3
echo "3" > /data/zookeeper/myid
```

### 2. 启动 ZooKeeper 集群

```bash
# 在每个 ZooKeeper 节点上启动
zkServer.sh start

# 检查状态
zkServer.sh status
```

## 集群启动和验证

### 1. 启动顺序

```bash
# 1. 启动 Hadoop 集群
start-dfs.sh

# 2. 启动 ZooKeeper 集群（如果使用外部 ZooKeeper）
# 在每个 ZooKeeper 节点执行
zkServer.sh start

# 3. 启动 HBase 集群
start-hbase.sh
```

### 2. 验证集群状态

#### 检查 HDFS 状态

```bash
hdfs dfsadmin -report
hdfs dfs -ls /
```

#### 检查 ZooKeeper 状态

```bash
# 检查 ZooKeeper 集群状态
for i in {1..3}; do
  echo "=== node${i} ZooKeeper Status ==="
  ssh node${i}.example.com "zkServer.sh status"
done
```

#### 检查 HBase 状态

```bash
# 使用 HBase shell
hbase shell
hbase> status
hbase> list

# 检查 Web UI
# Master: http://node1.example.com:16010
# RegionServer: http://node2.example.com:16030
```

### 3. 验证进程运行

```bash
# 在 Master 节点检查进程
jps
# 应该看到：HMaster, HQuorumPeer

# 在 RegionServer 节点检查进程
jps
# 应该看到：HRegionServer, HQuorumPeer, DataNode
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查防火墙设置
   - 确认 ZooKeeper 端口（默认 2181）是否开放

2. **认证失败**
   - 验证 keytab 文件权限和路径
   - 检查 Kerberos 主体名称是否正确
   - 确认时间同步（Kerberos 对时间敏感）

3. **会话超时**
   - 调整 `zookeeper.session.timeout` 参数
   - 检查网络延迟和 JVM 垃圾回收设置

### 测试连接

使用 HBase shell 测试连接：

```bash
bin/hbase shell
hbase> list
```

## 参考文档

本文档基于以下官方资源整理：

1. **Apache HBase 官方文档 - ZooKeeper 章节**
   - 来源：https://hbase.apache.org/book.html#zk.sasl.auth
   - 版本：Apache HBase 官方文档

2. **主要参考的代码片段来源**：
   - HBase ZooKeeper 配置示例
   - JAAS 配置模板
   - 环境变量设置指南
   - 日志验证示例

3. **相关 JIRA 问题**：
   - HBASE-11349：Thrift 1 代理用户支持
   - HBASE-11474：Thrift 2 代理用户支持
   - HBASE-2611：复制失败场景修复

---

**注意**：本文档中的配置示例需要根据实际环境进行调整，包括主机名、域名、keytab 路径等。在生产环境中部署前，请务必在测试环境中验证配置的正确性。
