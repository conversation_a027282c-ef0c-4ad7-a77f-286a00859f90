---
# tags: deploy_redis

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/ops"
  register: users_output

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 传包-upgrade-web
  copy:
    src: "ops/upgrade-web"
    dest: "/home/<USER>/ops/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的日志文件
  file:
    path: "/home/<USER>/ops/upgrade-web/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    # recurse: yes
    state: touch
  with_items:
    - log/error.log

- name: load upgrade-web image
  command: "docker load -i /home/<USER>/ops/upgrade-web/images/{{ ansible_architecture|lower }}/nginx.tar"

- name: load ansible upgrade image
  command: "docker load -i /home/<USER>/ops/upgrade-web/images/{{ ansible_architecture|lower }}/ansible.tar.gz"
  
- name: 配置upgrade-web的config文件x86_64
  template:
    src: "upgrade-web/{{ item.key }}"
    dest: "/home/<USER>/ops/upgrade-web/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'upgrade-web.conf.j2' , value: 'config/conf.d/upgrade-web.conf' }
  when: ansible_architecture|lower == "x86_64"

- name: 配置upgrade-web服务配置文件及脚本aarch64
  template:
    src: "upgrade-web/{{ item.key }}"
    dest: "/home/<USER>/ops/upgrade-web/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'upgrade-web_aarch64.conf.j2' , value: 'config/conf.d/upgrade-web.conf' }
  when: ansible_architecture|lower == "aarch64"

- name: 配置upgrade-web的bin启动脚本service.sh
  template:
    force: yes
    src: upgrade-web/service.sh.j2
    dest: /home/<USER>/ops/upgrade-web/bin/service.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: list
  shell: ls
  notify:
    - restart upgrade-web

- name: 提前重启服务
  meta: flush_handlers
