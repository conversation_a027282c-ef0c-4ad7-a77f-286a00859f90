# 东莞银行风险管控系统开机自启动配置说明

## 📋 为什么需要 rc.local

您提到的 `rc.local` 确实是一个重要的配置！在企业级部署中，开机自启动是确保系统稳定性的关键环节。我们提供了多种自启动方案：

### 🔄 自启动方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **rc.local** | 简单直观、兼容性好 | 启动顺序难控制 | 传统环境、简单部署 |
| **systemd** | 依赖管理、日志完善 | 配置复杂 | 现代 Linux 系统 |
| **crontab @reboot** | 用户级别、灵活 | 无依赖管理 | 特定用户服务 |

## 🚀 rc.local 配置详解

### 1. 基本配置
```bash
# 编辑 rc.local 文件
vim /etc/rc.d/rc.local

# 添加执行权限
chmod +x /etc/rc.d/rc.local

# 启用 rc-local 服务（CentOS 7/8）
systemctl enable rc-local
systemctl start rc-local
```

### 2. 启动顺序设计
我们的 rc.local 脚本按以下顺序启动服务：

```
系统启动
    ↓
等待 30 秒（确保系统完全启动）
    ↓
启动 Redis（基础缓存服务）
    ↓
等待 10 秒
    ↓
启动 Zookeeper（协调服务）
    ↓
等待 15 秒
    ↓
启动 Kafka（消息队列）
    ↓
等待 10 秒
    ↓
启动应用服务（业务服务）
```

### 3. 日志记录机制
- **启动日志**: `/export/logs/system_startup.log`
- **关机日志**: `/export/logs/system_shutdown.log`
- **服务日志**: `/export/logs/service_manager_verbose.log`

## 🔧 正确的自启动配置方式

### 最佳实践：分离脚本和调用

您说得很对！直接在 `/etc/rc.d/rc.local` 中写大量代码不是最佳实践。正确的做法是：

1. **创建专门的启动脚本** - `/export/bin/system_startup.sh`
2. **rc.local 只负责调用** - 保持简洁

### 1. 创建专门的启动脚本
```bash
# /export/bin/system_startup.sh
#!/bin/bash
# 东莞银行风险管控系统开机自启动脚本

# 等待系统完全启动
sleep 30

# 设置日志文件
STARTUP_LOG="/export/logs/system_startup.log"
mkdir -p /export/logs

echo "=========================================" >> $STARTUP_LOG
echo "$(date) - 系统启动，开始启动服务..." >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

# 启动基础服务
echo "$(date) - 启动 Redis..." >> $STARTUP_LOG
systemctl start redis >> $STARTUP_LOG 2>&1

# 等待基础服务启动
sleep 10

# 启动 Zookeeper
echo "$(date) - 启动 Zookeeper..." >> $STARTUP_LOG
if [ -f "/export/server/zookeeper/bin/service.sh" ]; then
    su - appuser -c "/export/server/zookeeper/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 15
fi

# 启动 Kafka
echo "$(date) - 启动 Kafka..." >> $STARTUP_LOG
if [ -f "/export/server/kafka/bin/service.sh" ]; then
    su - appuser -c "/export/server/kafka/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 10
fi

# 启动应用服务
echo "$(date) - 启动应用服务..." >> $STARTUP_LOG
if [ -f "/export/bin/manage_services_verbose.sh" ]; then
    su - appuser -c "/export/bin/manage_services_verbose.sh start" >> $STARTUP_LOG 2>&1
fi

echo "$(date) - 所有服务启动完成" >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

# 可选：发送启动完成通知
# echo "东莞银行风险管控系统启动完成" | mail -s "系统启动通知" <EMAIL>

exit 0
```

### 2. rc.local 简洁调用
```bash
# /etc/rc.d/rc.local
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 调用专门的启动脚本（后台执行）
/export/bin/system_startup.sh &

exit 0
```

### 为什么这样做更好？

1. **职责分离** - rc.local 只负责调用，业务逻辑在专门脚本中
2. **易于维护** - 修改启动逻辑不需要 root 权限编辑 rc.local
3. **便于测试** - 可以单独测试启动脚本
4. **版本控制** - 启动脚本可以纳入版本管理
5. **权限管理** - 启动脚本可以设置为 appuser 所有
6. **日志管理** - 启动脚本的日志独立管理
7. **错误处理** - 更容易添加错误处理和重试机制

### 3. 更优雅的调用方式
```bash
# 方式一：后台执行（推荐）
/export/bin/system_startup.sh &

# 方式二：使用 nohup（防止终端关闭影响）
nohup /export/bin/system_startup.sh > /dev/null 2>&1 &

# 方式三：使用 systemd-run（现代方式）
systemd-run --no-block /export/bin/system_startup.sh

# 方式四：调用现有的启动脚本
/export/bin/start_all_services.sh &
```

### 4. 配置文件权限设置
```bash
# 设置启动脚本权限
chmod +x /export/bin/system_startup.sh
chown appuser:appuser /export/bin/system_startup.sh

# 设置 rc.local 权限
chmod +x /etc/rc.d/rc.local

# 验证权限
ls -la /etc/rc.d/rc.local
ls -la /export/bin/system_startup.sh
```

## 🛡️ 优雅关机配置

### 关机脚本
```bash
#!/bin/bash
# /export/bin/shutdown_services.sh

SHUTDOWN_LOG="/export/logs/system_shutdown.log"

echo "=========================================" >> $SHUTDOWN_LOG
echo "$(date) - 系统关机，开始停止服务..." >> $SHUTDOWN_LOG
echo "=========================================" >> $SHUTDOWN_LOG

# 停止应用服务
echo "$(date) - 停止应用服务..." >> $SHUTDOWN_LOG
if [ -f "/export/bin/manage_services_verbose.sh" ]; then
    su - appuser -c "/export/bin/manage_services_verbose.sh stop" >> $SHUTDOWN_LOG 2>&1
fi

# 等待应用服务完全停止
sleep 10

# 停止 Kafka
echo "$(date) - 停止 Kafka..." >> $SHUTDOWN_LOG
if [ -f "/export/server/kafka/bin/service.sh" ]; then
    su - appuser -c "/export/server/kafka/bin/service.sh stop" >> $SHUTDOWN_LOG 2>&1
    sleep 5
fi

# 停止 Zookeeper
echo "$(date) - 停止 Zookeeper..." >> $SHUTDOWN_LOG
if [ -f "/export/server/zookeeper/bin/service.sh" ]; then
    su - appuser -c "/export/server/zookeeper/bin/service.sh stop" >> $SHUTDOWN_LOG 2>&1
fi

# 停止 Redis
echo "$(date) - 停止 Redis..." >> $SHUTDOWN_LOG
systemctl stop redis >> $SHUTDOWN_LOG 2>&1

echo "$(date) - 所有服务停止完成" >> $SHUTDOWN_LOG
echo "=========================================" >> $SHUTDOWN_LOG

exit 0
```

## 🔍 验证自启动配置

### 1. 检查 rc.local 状态
```bash
# 检查 rc.local 文件权限
ls -la /etc/rc.d/rc.local

# 检查 rc-local 服务状态
systemctl status rc-local

# 查看 rc-local 服务日志
journalctl -u rc-local -f
```

### 2. 测试自启动
```bash
# 重启系统测试
reboot

# 重启后检查服务状态
/export/bin/check_services.sh

# 查看启动日志
tail -f /export/logs/system_startup.log
```

### 3. 手动测试脚本
```bash
# 手动执行 rc.local 脚本
/etc/rc.d/rc.local

# 检查执行结果
echo $?
```

## ⚠️ 注意事项

### 1. 权限问题
- rc.local 必须有执行权限
- 服务脚本必须以正确用户身份运行
- 日志目录必须可写

### 2. 启动时间
- 系统启动后等待 30 秒再启动服务
- 各服务间有适当的启动间隔
- 总启动时间约 2-3 分钟

### 3. 错误处理
- 所有操作都记录日志
- 脚本具有容错机制
- 单个服务失败不影响其他服务

### 4. 兼容性
- 适用于 CentOS 6/7/8
- 兼容 RHEL 系列系统
- 支持传统 SysV 和 systemd

## 🔄 替代方案

### 1. systemd 服务（推荐）
```bash
# 创建 systemd 服务
systemctl enable dongguanbank-risk.service
```

### 2. crontab 定时任务
```bash
# 添加开机启动任务
echo "@reboot /export/bin/start_all_services.sh" | crontab -
```

### 3. supervisor 进程管理
```bash
# 使用 supervisor 管理进程
yum install supervisor
```

## 📊 监控和维护

### 1. 日志轮转
```bash
# 配置日志轮转
cat > /etc/logrotate.d/dongguanbank << 'EOF'
/export/logs/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 appuser appuser
}
EOF
```

### 2. 健康检查
```bash
# 创建健康检查脚本
cat > /export/bin/health_check.sh << 'EOF'
#!/bin/bash
# 每5分钟检查一次服务状态
*/5 * * * * /export/bin/check_services.sh > /dev/null 2>&1
EOF
```

---

**配置完成后，系统将在每次重启时自动启动所有服务，确保东莞银行风险管控系统的高可用性。**
