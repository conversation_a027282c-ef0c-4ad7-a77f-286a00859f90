---
# tags: deploy_kibana

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: copy kibana
  unarchive:
    src: "server/kibana/{{ ansible_architecture|lower }}/{{ kibanaXpack_tarball_name }}"
    dest: "/home/<USER>/server"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
#    force: yes
    mode: 0755

- name: kibana软连接到/home/<USER>/server/kibana
  file:
    src: "/home/<USER>/server/kibana-6.8.5"
    dest: "/home/<USER>/server/kibana"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: ansible_architecture|lower == "x86_64"

- name: chmod kibana bin
  command: "chmod -R 755 /home/<USER>/server/kibana"

- name: 配置kibana服务配置文件及脚本
  template:
    src: "kibana/{{ item.key }}"
    dest: "/home/<USER>/server/kibana/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'kibana.yml.j2' , value: 'config/kibana.yml' }
    - { key: 'bangcle_kibana_dog.sh.j2' , value: 'bin/bangcle_kibana_dog.sh' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/kibana/bin/service.sh"
    path: "/home/<USER>/server/kibana/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/kibana/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "kibana/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/kibana/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: 在kibana配置文件增加用户认证
  lineinfile:
    path: /home/<USER>/server/kibana/config/kibana.yml
    line: "{{ item }}"
  with_items:
    - 'elasticsearch.username: "elastic"'
    - 'elasticsearch.password: "beap123"'
  when: 
    - add_esxpack == true
    - ansible_architecture|lower == "x86_64"

- name: ready to notify
  shell: ls
  notify:
    - start kibana
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers

