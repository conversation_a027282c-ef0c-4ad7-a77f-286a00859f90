---

- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

- name: check docker.pid
  stat:
    path: "/run/docker.pid"
  register: dockerpid_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- import_tasks: checkHadoopHA.yml

- name: check openjdk17是否存在
  stat: 
    path: "/home/<USER>/tools/jdk-17/"
  register: check_jdk
  ignore_errors: True

- name: 传openjdk17u
  unarchive:
    src: "tools/jdk/{{ ansible_architecture|lower }}/{{ openjdk_tarball_name }}"
    dest: "/home/<USER>/tools"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_jdk.stat.exists == False

- name: openjdk做软连接
  file:
    src: "/home/<USER>/tools/{{ openjdk_name }}"
    dest: "/home/<USER>/tools/jdk-17"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    state: link
    follow: False
  when: check_jdk.stat.exists == False

- name: 配置环境变量
  template:
    src: bangcleUser/bash_profile.j2
    dest: /home/<USER>/.bash_profile
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_jdk.stat.exists == False

## 备份init的hadoop和es，用于第二次升级需要文件
- name: check init 是否安装
  stat: 
    path: "/home/<USER>/app/init/"
  register: check_server
  ignore_errors: True

- name: 备份init服务
  command: "cp -a /home/<USER>/app/init /tmp/"
  when: 
    - check_server.stat.exists == True

- import_tasks: backupWxgzApp.yml
  vars:
    backup_server: init
  when: 
    - check_server.stat.exists == True

- name: 从everisk_tag提取版本号
  set_fact:
    wxgz_version: "{{ everisk_tag.split('_')[0].split('ver')[1] }}"

#- name: 比较wxgz_version与hbase_compare_version
#  debug:
#    msg: "wxgz_version 大于或等于 hbase_compare_version"
#  when: wxgz_version is version(hbase_compare_version, '>=')

- name: 删除init历史文件
  file:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init"
    state: absent
  ignore_errors: True
  connection: local

- name: 解压init包到本地 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/{{ init_tarball_name }}"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/"
    # mode: 0777
  # command: "tar zxvf {{ inventory_dir }}/roles/{{ role_name }}/files/{{ init_tarball_name }} -C {{ inventory_dir }}/roles/{{ role_name }}/files/app/"
  connection: local

- name: 查找所有的配置文件
  find:
    paths: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init/config/"
    # paths: "/home/<USER>/app/init"
    recurse: yes
    file_type: file
    patterns: [ "everisk-v*.json","everisk.json","application*.properties","hbase-site.xml" ]
  register: config_files
  connection: local

- name: 返回config_files变量
  debug: 
    msg: "{{ config_files }}"

- name: 替换配置文件中的占位符
  template:
    src: "{{ item }}"
    dest: "{{ item }}"
    trim_blocks: no
  with_items: "{{ config_files.files | map(attribute='path') | list }}"
  connection: local

- name: 将init重新打包
  archive:
    path: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init"
    dest: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init-modify.tar.gz"
    format: "gz"
    mode: 0644
  connection: local

- name: 安装init包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init-modify.tar.gz"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: 配置log4j2
  template:
    src: "log4j2/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'log4j2.xml.j2' , value: 'config/log4j2.xml' }

- name: 查找hbase-site.xml
  find:
    # paths: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init/config/"
    paths: "/home/<USER>/app/init"
    recurse: yes
    file_type: file
    patterns: [ "hbase-site.xml" ]
  register: hbase_site

- name: 配置init的hbase-site.xml
  template:
    src: hbase/hbase-site.xml.j2
    dest: "{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    trim_blocks: no
  with_items: "{{ hbase_site.files | map(attribute='path') | list }}"

- name: 查找core-site.xml
  find:
    # paths: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init/config/"
    paths: "/home/<USER>/app/init"
    recurse: yes
    file_type: file
    patterns: [ "core-site.xml" ]
  register: core_site 

- name: 配置init的core-site.xml
  template:
    src: hadoop/core-site.xml.j2
    dest: "{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    trim_blocks: no
  with_items: "{{ core_site.files | map(attribute='path') | list }}"
  when: 
    - hdfs_tag == True

- name: 查找hdfs-site.xml
  find:
    # paths: "{{ inventory_dir }}/roles/{{ role_name }}/files/app/init/config/"
    paths: "/home/<USER>/app/init"
    recurse: yes
    file_type: file
    patterns: [ "hdfs-site.xml" ]
  register: hdfs_site 

- name: 配置init的hdfs-site.xml
  template:
    src: hadoop/hdfs-site.xml.j2
    dest: "{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    trim_blocks: no
  with_items: "{{ hdfs_site.files | map(attribute='path') | list }}"
  when: 
    - hdfs_tag == True

## redis cluster调整配置文件
- name: 调整redis cluster
  lineinfile: 
    dest: "/home/<USER>/app/init/config/everisk.json"
    regexp: '"active_mode": "single"'
    line: '"active_mode": "cluster"'
  when:
    - groups['redis'] | length  >= 2

- name: 配置执行权限
  command: "chmod +x /home/<USER>/app/init/bin/service.sh"

- name: 调整init的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/init/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

### 当升级init后，回取刚刚备份的文件
- name: check  init服务是否有备份 
  stat: 
    path: "/tmp/init/"
  register: check_backup_server
  ignore_errors: True

- name: 还原hadoop配置文件
  command: "cp -a /tmp/init/config/everisk/hadoop/. /home/<USER>/app/init/config/everisk/hadoop/"
  when: 
    - check_backup_server.stat.exists == True

- name: 还原elasticsearch配置文件
  command: "cp -a /tmp/init/config/everisk/elasticsearch/. /home/<USER>/app/init/config/everisk/elasticsearch/"
  when: 
    - check_backup_server.stat.exists == True

- name: 删除备份的init历史文件
  file:
    path: "/tmp/init"
    state: absent
  ignore_errors: True

- name: 创建images目录
  file:
    path: "/home/<USER>/app/init/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - images

- name: 传包-init镜像
  copy:
    src: "{{ init_img_tarball_name}}"
    dest: "/home/<USER>/app/init/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 加载images
  command: "docker load -i /home/<USER>/app/init/images/init.tar"

- name: 配置init的config文件
  template:
    src: "init/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length == 1

- name: 配置init的config文件
  template:
    src: "init/{{ item.key }}"
    dest: "/home/<USER>/app/init/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.multi.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length > 1

- name: 解决init目录权限问题
  command: "chown {{ bangcle_user }}. -R /home/<USER>/app/init"

#- name: 解决app的log目录内文件bangcle用户无法读取
#  shell: "sudo chmod 666 /home/<USER>/app/init/log/*"
#  when:
#    - ansible_architecture|lower == "aarch64"
#  ignore_errors: True

# - name: check  是否有证书
#   stat: 
#     path: "/home/<USER>/app/init/config/everisk/elasticsearch/certs/bangcle.crt"
#   register: check_es_key
#   ignore_errors: True

- name: 将elasticsearch-xpack证书cert文件拷贝到init配置文件目录
  template:
    src: "xpack/{{ item.key }}"
    dest: "/home/<USER>/app/init/config/everisk/elasticsearch/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'bangcle.crt.j2' , value: 'certs/bangcle.crt' }
    - { key: 'bangcle.key.j2' , value: 'certs/bangcle.key' }
    - { key: 'ca.crt.j2' , value: 'certs/ca.crt' }
    - { key: 'ca.key.j2' , value: 'certs/ca.key' }
    - { key: 'es-client-settings.properties.j2' , value: 'es-client-settings.properties' }
  when: 
    - add_esxpack == true
    - ansible_architecture|lower == "x86_64"
    # - check_es_key.stat.exists != True

- name: ready to notify
  shell: ls
  notify:
    - restart init

- name: 提前重启服务
  meta: flush_handlers
