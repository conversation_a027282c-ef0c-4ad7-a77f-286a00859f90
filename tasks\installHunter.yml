---
#- tags: install_hunter

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
- import_tasks: backupApp.yml
  vars: 
    backup_server: hunter

- name: 安装hunter包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ hunter_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/hunter/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/hunter/config/application-zk.properties"
    dest: "./roles/{{ role_name }}/templates/hunter/application-zk.properties.j2"
    flat: yes
  when: check_application_zk.stat.exists == True

- name: 配置hunter的application-zk.properties
  template:
    src: hunter/application-zk.properties.j2
    dest: /home/<USER>/app/hunter/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_zk.stat.exists == True

- name: check  application-remote.properties
  stat: 
    path: "/home/<USER>/app/hunter/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/hunter/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/hunter/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置hunter的application-remote.properties
  template:
    src: hunter/application-remote.properties.j2
    dest: /home/<USER>/app/hunter/config/application-remote.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  when: check_application_remote.stat.exists == True

- name: configure startup.sh
  template:
    src: "hunter/startup.sh.j2"
    dest: "/home/<USER>/app/hunter/bin/startup.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

# - name: 配置hunter的hbase-site.xml
#   template:
#     src: hbase-site.xml.j2
#     dest: /home/<USER>/app/hunter/config/hbase-site.xml

- name: 调整hunter的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/hunter/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/hunter/bin/service.sh"
    path: "/home/<USER>/app/hunter/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/hunter/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "hunter/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/hunter/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart hunter
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
