---
#- tags: install_threat

- import_tasks: createUser.yml

- name: 安装tools-sql包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ toolssql_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  notify:
    - restart tools-sql

- name: 配置tools-sql的service.sh
  template:
    src: tools-sql/service.sh.j2
    dest: /home/<USER>/app/tools-sql/bin/service.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: check  application-zk.properties
  stat: 
    path: "/home/<USER>/app/tools-sql/config/application-zk.properties"
  register: check_application_zk
  ignore_errors: True

# - name: fetch application property file
#   fetch:
#     src: "/home/<USER>/app/tools-sql/config/application-zk.properties"
#     dest: "./roles/{{ role_name }}/templates/tools-sql/application-zk.properties.j2"
#     flat: yes
#   when: check_application_zk.stat.exists == True

- name: 配置tools-sql的application-zk.properties
  template:
    src: tools-sql/application-zk.properties.j2
    dest: /home/<USER>/app/tools-sql/config/application-zk.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: check  application-remote.properties
  stat: 
    path: "/home/<USER>/app/tools-sql/config/application-remote.properties"
  register: check_application_remote
  ignore_errors: True

- name: fetch application property file
  fetch:
    src: "/home/<USER>/app/tools-sql/config/application-remote.properties"
    dest: "./roles/{{ role_name }}/templates/tools-sql/application-remote.properties.j2"
    flat: yes
  when: check_application_remote.stat.exists == True

- name: 配置tools-sql的application-remote.properties
  template:
    src: tools-sql/application-remote.properties.j2
    dest: /home/<USER>/app/tools-sql/config/application-remote.properties
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  when: check_application_remote.stat.exists == True

- name: 调整tools-sql的启动用户
  lineinfile: 
    dest: "/home/<USER>/app/tools-sql/bin/service.sh"
    regexp: "^START_USER="
    line: "START_USER={{ bangcle_user }}"


- name: 提前重启服务
  meta: flush_handlers