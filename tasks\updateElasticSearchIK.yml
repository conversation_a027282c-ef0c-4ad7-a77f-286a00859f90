---
# 升级elasticSearch的ik分词
- name: check  elasticSearchMaster是否需要升级
  stat: 
    path: "/home/<USER>/server/elasticsearchMaster/elasticsearch/plugins/ik"
  register: es_master_ik
  ignore_errors: True

- name: check  elasticSearchMaster是否存在
  stat: 
    path: "/home/<USER>/server/elasticsearchMaster/elasticsearch"
  register: es_master
  ignore_errors: True

- name: 更新es的ik
  copy:
    src: "server/elasticsearch/elasticsearch/plugins/ik"
    dest: "/home/<USER>/server/elasticsearchMaster/elasticsearch/plugins/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
  notify:
    - restart elasticsearchMaster
  when: 
    - es_master_ik.stat.exists != True
    - es_master.stat.exists == True
  

- name: 提前重启服务
  meta: flush_handlers

- name: check  elasticSearchClient是否需要升级
  stat: 
    path: "/home/<USER>/server/elasticsearchClient/elasticsearch/plugins/ik"
  register: es_client_ik
  ignore_errors: True

- name: check  elasticSearchClient是否存在
  stat: 
    path: "/home/<USER>/server/elasticsearchClient/elasticsearch"
  register: es_client
  ignore_errors: True

- name: 更新es的ik
  copy:
    src: "server/elasticsearch/elasticsearch/plugins/ik"
    dest: "/home/<USER>/server/elasticsearchClient/elasticsearch/plugins/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
  notify:
    - restart elasticsearchClient
  when: 
    - es_client_ik.stat.exists != True
    - es_client.stat.exists == True

- name: 提前重启服务
  meta: flush_handlers