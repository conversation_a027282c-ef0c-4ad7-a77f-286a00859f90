# systemd 与 rc.local 结合使用说明

## 🤔 为什么要结合使用？

您的建议很有价值！将 systemd 服务通过 rc.local 启动确实是一个很好的方案，结合了两种方式的优点：

### 优势分析

| 特性 | 纯 systemd | 纯 rc.local | systemd + rc.local |
|------|------------|-------------|-------------------|
| **标准化** | ✅ 高 | ❌ 低 | ✅ 高 |
| **兼容性** | ❌ 现代系统 | ✅ 所有系统 | ✅ 所有系统 |
| **日志管理** | ✅ 完善 | ❌ 简单 | ✅ 完善 |
| **依赖管理** | ✅ 完善 | ❌ 无 | ✅ 完善 |
| **控制灵活性** | ❌ 固定 | ✅ 灵活 | ✅ 灵活 |
| **故障恢复** | ✅ 自动 | ❌ 手动 | ✅ 自动 |

## 🔧 三种实现方案

### 方案一：纯 systemd（标准方式）
```bash
# 创建服务文件
systemctl enable dongguanbank-risk.service
systemctl start dongguanbank-risk.service

# 优点：标准化、自动重启、完善日志
# 缺点：在某些环境下可能启动时机不对
```

### 方案二：rc.local + systemd（推荐）
```bash
# /etc/rc.d/rc.local
#!/bin/bash
systemctl start dongguanbank-risk.service
exit 0

# 优点：兼容性好、启动时机可控、保留 systemd 优势
# 缺点：需要两层配置
```

### 方案三：混合容错模式（最稳定）
```bash
# /etc/rc.d/rc.local
#!/bin/bash
if systemctl start dongguanbank-risk.service; then
    echo "systemd 启动成功"
else
    echo "systemd 启动失败，使用脚本模式"
    /export/bin/system_startup.sh &
fi
exit 0

# 优点：最大兼容性、容错性强
# 缺点：逻辑稍复杂
```

## 📋 完整配置示例

### 1. 创建 systemd 服务文件
```bash
cat > /etc/systemd/system/dongguanbank-risk.service << 'EOF'
[Unit]
Description=东莞银行风险管控系统
Documentation=file:///export/docs/deployment.md
After=network.target multi-user.target
Wants=network.target
RequiresMountsFor=/export

[Service]
Type=forking
User=root
Group=root
ExecStart=/export/bin/start_all_services.sh
ExecStop=/export/bin/manage_services_verbose.sh stop
ExecReload=/export/bin/manage_services_verbose.sh restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=no
RemainAfterExit=yes
StandardOutput=journal
StandardError=journal

# 环境变量
Environment=JAVA_HOME=/export/tools/jdk
Environment=PATH=/export/tools/jdk/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin

# 工作目录
WorkingDirectory=/export

[Install]
WantedBy=multi-user.target
EOF

# 重新加载配置
systemctl daemon-reload
```

### 2. 配置 rc.local 调用
```bash
cat > /etc/rc.d/rc.local << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 日志文件
RC_LOG="/export/logs/rc_local.log"
mkdir -p /export/logs

# 记录启动时间
echo "=========================================" >> $RC_LOG
echo "$(date) - rc.local 开始执行" >> $RC_LOG
echo "=========================================" >> $RC_LOG

# 等待系统稳定
sleep 10

# 启动方式配置
STARTUP_MODE="systemd"  # 可选：script, systemd, hybrid

case "$STARTUP_MODE" in
    "script")
        echo "$(date) - 使用脚本模式启动" >> $RC_LOG
        /export/bin/system_startup.sh >> $RC_LOG 2>&1 &
        ;;
    "systemd")
        echo "$(date) - 使用 systemd 模式启动" >> $RC_LOG
        systemctl start dongguanbank-risk.service >> $RC_LOG 2>&1
        ;;
    "hybrid")
        echo "$(date) - 使用混合模式启动" >> $RC_LOG
        if systemctl start dongguanbank-risk.service >> $RC_LOG 2>&1; then
            echo "$(date) - systemd 服务启动成功" >> $RC_LOG
        else
            echo "$(date) - systemd 启动失败，尝试脚本模式" >> $RC_LOG
            /export/bin/system_startup.sh >> $RC_LOG 2>&1 &
        fi
        ;;
    *)
        echo "$(date) - 未知模式，使用默认脚本启动" >> $RC_LOG
        /export/bin/system_startup.sh >> $RC_LOG 2>&1 &
        ;;
esac

echo "$(date) - rc.local 执行完成" >> $RC_LOG
echo "=========================================" >> $RC_LOG

exit 0
EOF

# 设置权限
chmod +x /etc/rc.d/rc.local
```

## 🔍 验证和测试

### 1. 测试 systemd 服务
```bash
# 手动测试服务
systemctl start dongguanbank-risk.service
systemctl status dongguanbank-risk.service
systemctl stop dongguanbank-risk.service

# 查看服务日志
journalctl -u dongguanbank-risk.service -f
```

### 2. 测试 rc.local
```bash
# 手动执行 rc.local
/etc/rc.d/rc.local

# 查看 rc.local 日志
tail -f /export/logs/rc_local.log

# 检查 rc-local 服务状态
systemctl status rc-local
```

### 3. 完整重启测试
```bash
# 重启系统测试
reboot

# 重启后检查
systemctl status dongguanbank-risk.service
/export/bin/check_services.sh
tail -f /export/logs/rc_local.log
```

## 🎯 使用场景建议

### 场景一：现代 Linux 环境（CentOS 7/8, Ubuntu 16+）
```bash
# 推荐：rc.local + systemd
STARTUP_MODE="systemd"
```

### 场景二：传统 Linux 环境（CentOS 6, 老版本系统）
```bash
# 推荐：纯脚本模式
STARTUP_MODE="script"
```

### 场景三：混合环境或不确定环境
```bash
# 推荐：混合容错模式
STARTUP_MODE="hybrid"
```

### 场景四：高可用生产环境
```bash
# 推荐：systemd + 监控
STARTUP_MODE="systemd"
# 配合 systemd 的 Restart=on-failure
```

## ⚙️ 高级配置选项

### 1. 启动延迟配置
```bash
# 在 rc.local 中添加智能等待
wait_for_network() {
    local count=0
    while [ $count -lt 30 ]; do
        if ping -c 1 ******* >/dev/null 2>&1; then
            return 0
        fi
        sleep 2
        count=$((count + 1))
    done
    return 1
}

# 等待网络就绪
if wait_for_network; then
    echo "网络就绪，开始启动服务"
    systemctl start dongguanbank-risk.service
else
    echo "网络未就绪，使用本地模式启动"
    /export/bin/system_startup.sh &
fi
```

### 2. 条件启动配置
```bash
# 根据系统状态决定启动方式
if [ -f "/export/.maintenance_mode" ]; then
    echo "系统处于维护模式，跳过自动启动"
    exit 0
fi

if [ "$(systemctl is-enabled dongguanbank-risk.service)" = "enabled" ]; then
    echo "服务已启用，使用 systemctl 启动"
    systemctl start dongguanbank-risk.service
else
    echo "服务未启用，使用脚本启动"
    /export/bin/system_startup.sh &
fi
```

## 📊 监控和维护

### 1. 启动状态监控
```bash
# 创建启动状态检查脚本
cat > /export/bin/check_startup_status.sh << 'EOF'
#!/bin/bash
echo "=== 启动状态检查 ==="
echo "rc-local 服务状态: $(systemctl is-active rc-local)"
echo "dongguanbank-risk 服务状态: $(systemctl is-active dongguanbank-risk.service)"
echo "应用服务状态:"
/export/bin/check_services.sh
EOF

chmod +x /export/bin/check_startup_status.sh
```

### 2. 日志轮转配置
```bash
# 配置 rc.local 日志轮转
cat > /etc/logrotate.d/rc-local << 'EOF'
/export/logs/rc_local.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF
```

## 🎉 总结

通过 rc.local 启动 systemd 服务是一个很好的方案，它：

1. **保留了 systemd 的优势** - 日志管理、依赖控制、自动重启
2. **增加了启动的可控性** - 可以在 rc.local 中添加条件判断
3. **提高了兼容性** - 适用于各种 Linux 环境
4. **便于故障排除** - 多层日志记录，便于定位问题

这种方式特别适合企业级部署，既保证了标准化，又保持了灵活性！
