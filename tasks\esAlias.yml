---
# tags: esAlias

- name: 配置esAlias脚本
  template:
    src: bangcleUser/index_operate.sh.j2
    dest: /home/<USER>/bin/index_operate.sh
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 清除多次升级esAlias的index文件
  file:
    path: "/home/<USER>/bin/indexs.txt"
    force: yes
    state: absent
  ignore_errors: True

- name: 系统配置文件sysctl生效
  shell: "su - {{ bangcle_user }} -c \"bash bin/index_operate.sh {{ groups['elasticsearchClient'][0] }}:{{ elasticsearch_client_restful_port }}  alias \" "