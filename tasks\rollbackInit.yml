---

- name: "check  {{ rollback_app }}是否安装"
  stat: 
    path: "/home/<USER>/app/{{ rollback_app }}"
  register: check_server
  ignore_errors: True

- name: "停止{{ rollback_app }}服务"
  command: "ls"
  notify:
    - stop {{ rollback_app }}
  when:
    - check_server.stat.exists == True
  ignore_errors: True

- name: 提前停止服务
  meta: flush_handlers
  ignore_errors: True

- name: "查找指定文件备份"
  find:
    recurse: no
    paths:
      - "/home/<USER>/backups"
    patterns:
      - '{{ rollback_app }}.bk-*'
    hidden: yes
    file_type: any
    use_regex: true
  register: backup_app

- name: 查找备份文件 #查找最后一个备份文件按照时间排序
  set_fact:
    latest_backup: "{{ (backup_app.files | sort(attribute='mtime') | last).path }}"

- name: 输出backup_app
  debug: 
    msg: "{{ backup_app.files | map(attribute='path') | list }}" 

- name: 输出latest_backup
  debug: 
    msg: "{{ latest_backup }}"

- name: drop {{ rollback_app }}
  file:
    path: "{{ item }}"
    state: absent
  with_items:
    - "/home/<USER>/app/{{ rollback_app }}"
  when:  
    - check_server.stat.exists == True
  ignore_errors: True

- name: 还原{{ rollback_app }}服务
  command: "cp -a {{ latest_backup }} /home/<USER>/app/{{ rollback_app }}"
  notify:
    - restart {{ rollback_app }}
  ignore_errors: True

- name: 提前重启服务
  meta: flush_handlers
  ignore_errors: True
