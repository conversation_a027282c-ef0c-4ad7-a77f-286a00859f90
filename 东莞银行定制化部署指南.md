# 东莞银行风险管控系统定制化部署指南

## 📋 环境配置信息

### 基本配置
- **部署用户**: `appuser`
- **部署根目录**: `/export`
- **用户组**: `appuser`, `agent`
- **主机命名**: `sh.bdc_tom.a`, `sh.bdc_tom.b`, `hl.bdc_tom.a`

### 目录结构规范
```
/export/
├── tools/          # 工具目录 (JDK等)
├── app/           # 应用程序目录
├── bin/           # 可执行脚本目录
├── config/        # 配置文件目录
├── data/          # 数据目录
├── logs/          # 日志目录
├── server/        # 服务器软件目录
└── source/        # 源码目录
```

## 🚀 第一阶段：环境准备

### 1.1 系统初始化
```bash
# 1. 关闭防火墙和 SELinux
systemctl stop firewalld
systemctl disable firewalld
setenforce 0
sed -i 's/SELINUX=enforcing/SELINUX=disabled/g' /etc/selinux/config

# 2. 系统参数优化
cat >> /etc/sysctl.conf << 'EOF'
fs.file-max=2048000
net.ipv4.tcp_tw_reuse=1
vm.swappiness=0
vm.max_map_count=262144
net.ipv4.ip_forward=1
net.ipv6.conf.all.forwarding=1
net.ipv4.tcp_fin_timeout=10
net.ipv4.tcp_tw_recycle=0
EOF

sysctl -p

# 3. 用户资源限制
cat >> /etc/security/limits.conf << 'EOF'
appuser soft nofile 65536
appuser hard nofile 65536
appuser soft memlock unlimited
appuser hard memlock unlimited
* soft nproc 65536
* - nofile 65536
EOF
```

### 1.2 创建部署用户
```bash
# 1. 创建用户组
groupadd agent
groupadd appuser

# 2. 创建用户
useradd -m -s /bin/bash -G agent appuser
echo "appuser:your_secure_password" | chpasswd

# 3. 创建 /export 目录结构
mkdir -p /export/{tools,app,bin,config,data,logs,server,source}
chown -R appuser:appuser /export
chmod -R 755 /export

# 4. 创建用户软链接 (如果需要)
ln -sf /export /home/<USER>/export
```

### 1.3 安装 JDK 环境
```bash
# 1. 解压 JDK 8
tar -xzf jdk-8u*-linux-x64.tar.gz -C /export/tools/
ln -sf /export/tools/jdk1.8.* /export/tools/jdk

# 2. 配置环境变量
cat >> /home/<USER>/.bash_profile << 'EOF'
export JAVA_HOME=/export/tools/jdk
export PATH=$JAVA_HOME/bin:$PATH
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar
EOF

# 3. 使环境变量生效
su - appuser -c "source ~/.bash_profile"
```

## 🔧 第二阶段：基础服务部署

### 2.1 Zookeeper 集群部署
```bash
# 1. 创建目录
mkdir -p /export/server/zookeeper/{bin,zk_data}

# 2. 解压安装包
tar -xzf apache-zookeeper-3.6.3-bin.tar.gz -C /export/server/zookeeper/
ln -sf /export/server/zookeeper/apache-zookeeper-3.6.3-bin /export/server/zookeeper/zookeeper

# 3. 配置 zoo.cfg
cat > /export/server/zookeeper/zookeeper/conf/zoo.cfg << 'EOF'
tickTime=2000
initLimit=10
syncLimit=5
dataDir=/export/server/zookeeper/zk_data
clientPort=2181
server.1=sh.bdc_tom.a:2888:3888
server.2=sh.bdc_tom.b:2888:3888
server.3=hl.bdc_tom.a:2888:3888
EOF

# 4. 设置 myid (每个节点不同)
# 在 sh.bdc_tom.a 节点执行:
echo "1" > /export/server/zookeeper/zk_data/myid
# 在 sh.bdc_tom.b 节点执行:
# echo "2" > /export/server/zookeeper/zk_data/myid
# 在 hl.bdc_tom.a 节点执行:
# echo "3" > /export/server/zookeeper/zk_data/myid

# 5. 创建启动脚本
cat > /export/server/zookeeper/bin/service.sh << 'EOF'
#!/bin/bash
ZK_HOME=/export/server/zookeeper/zookeeper
export JAVA_HOME=/export/tools/jdk

case $1 in
start)
    echo "Starting Zookeeper..."
    $ZK_HOME/bin/zkServer.sh start
    ;;
stop)
    echo "Stopping Zookeeper..."
    $ZK_HOME/bin/zkServer.sh stop
    ;;
restart)
    $0 stop
    sleep 5
    $0 start
    ;;
status)
    $ZK_HOME/bin/zkServer.sh status
    ;;
esac
EOF

chmod +x /export/server/zookeeper/bin/service.sh
chown -R appuser:appuser /export/server/zookeeper
```

### 2.2 Kafka 集群部署
```bash
# 1. 创建目录
mkdir -p /export/server/kafka/{data/kafka-logs,bin}

# 2. 解压安装包
tar -xzf kafka_2.13-2.8.0.tgz -C /export/server/kafka/
ln -sf /export/server/kafka/kafka_2.13-2.8.0 /export/server/kafka/kafka

# 3. 配置 server.properties
cat > /export/server/kafka/kafka/config/server.properties << 'EOF'
# 每个节点的 broker.id 不同
broker.id=1  # sh.bdc_tom.a=1, sh.bdc_tom.b=2, hl.bdc_tom.a=3
listeners=PLAINTEXT://0.0.0.0:9092
advertised.listeners=PLAINTEXT://sh.bdc_tom.a:9092  # 每个节点修改为对应主机名
log.dirs=/export/server/kafka/data/kafka-logs
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
num.partitions=8
num.recovery.threads.per.data.dir=1
offsets.topic.replication.factor=3
transaction.state.log.replication.factor=3
transaction.state.log.min.isr=2
log.retention.hours=168
log.segment.bytes=1073741824
log.retention.check.interval.ms=300000
zookeeper.connect=sh.bdc_tom.a:2181,sh.bdc_tom.b:2181,hl.bdc_tom.a:2181
zookeeper.connection.timeout.ms=18000
group.initial.rebalance.delay.ms=0
EOF

# 4. 创建启动脚本
cat > /export/server/kafka/bin/service.sh << 'EOF'
#!/bin/bash
KAFKA_HOME=/export/server/kafka/kafka
export JAVA_HOME=/export/tools/jdk

case $1 in
start)
    echo "Starting Kafka..."
    $KAFKA_HOME/bin/kafka-server-start.sh -daemon $KAFKA_HOME/config/server.properties
    ;;
stop)
    echo "Stopping Kafka..."
    $KAFKA_HOME/bin/kafka-server-stop.sh
    ;;
restart)
    $0 stop
    sleep 5
    $0 start
    ;;
status)
    ps aux | grep kafka | grep -v grep
    ;;
esac
EOF

chmod +x /export/server/kafka/bin/service.sh
chown -R appuser:appuser /export/server/kafka
```

## 💾 第三阶段：数据存储服务

### 3.1 TDSQL 数据库配置
```bash
# TDSQL 数据库连接配置
# 注意：TDSQL 是腾讯云分布式数据库，需要提前准备好连接信息

# 1. 创建数据库配置文件
mkdir -p /export/config/database

# 2. 配置数据库连接信息
cat > /export/config/database/tdsql.properties << 'EOF'
# TDSQL 数据库连接配置
db.driver=com.mysql.cj.jdbc.Driver
db.url=jdbc:mysql://your-tdsql-host:3306/everisk?useSSL=false&serverTimezone=Asia/Shanghai
db.username=everisk
db.password=everisk123
db.maxActive=20
db.initialSize=5
db.maxWait=60000
db.minIdle=5
db.timeBetweenEvictionRunsMillis=60000
db.minEvictableIdleTimeMillis=300000
db.validationQuery=SELECT 1
db.testWhileIdle=true
db.testOnBorrow=false
db.testOnReturn=false
EOF

# 3. 设置文件权限
chown appuser:appuser /export/config/database/tdsql.properties
chmod 600 /export/config/database/tdsql.properties

# 4. 创建数据库初始化脚本
cat > /export/config/database/init_tdsql.sql << 'EOF'
-- TDSQL 数据库初始化脚本
-- 请根据实际需要修改

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS everisk DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE everisk;

-- 创建用户表示例
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    status TINYINT DEFAULT 1,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 插入默认管理员用户
INSERT IGNORE INTO sys_user (username, password, email)
VALUES ('admin', 'admin123', '<EMAIL>');
EOF

echo "TDSQL 数据库配置完成"
echo "请根据实际 TDSQL 连接信息修改 /export/config/database/tdsql.properties"
```

### 3.2 Redis 缓存
```bash
# 1. 安装 Redis
yum install -y redis

# 2. 配置 redis.conf
sed -i "s/bind 127.0.0.1/bind 0.0.0.0/" /etc/redis.conf
sed -i "s/# requirepass foobared/requirepass redis123/" /etc/redis.conf
sed -i "s/# maxmemory <bytes>/maxmemory 2gb/" /etc/redis.conf

# 3. 启动服务
systemctl start redis
systemctl enable redis
```

## 📱 第四阶段：应用服务部署

### 4.1 应用服务端口分配
根据图片信息，应用服务端口分配如下：
- **init**: 8081
- **receiver**: 8082  
- **cleaner**: 8083
- **transfer**: 8080
- **threat**: 8084
- **web-service**: 8085

### 4.2 通用应用部署脚本
```bash
# 创建应用部署函数
deploy_app_service() {
    local service_name=$1
    local port=$2
    local memory=${3:-"2g"}

    echo "=========================================="
    echo "部署 $service_name 服务"
    echo "端口: $port"
    echo "内存: $memory"
    echo "目录: /export/app/$service_name"
    echo "=========================================="

    # 检查是否已存在
    if [ -d "/export/app/$service_name" ]; then
        echo "警告: 服务目录已存在，将备份原目录..."
        mv "/export/app/$service_name" "/export/app/$service_name.backup.$(date +%Y%m%d_%H%M%S)"
    fi

    # 创建目录结构
    mkdir -p /export/app/$service_name/{bin,config,lib,log,data,temp}

    # 检查应用包是否存在
    if [ ! -f "$service_name-1.0.0.tar.gz" ]; then
        echo "错误: 未找到应用包 $service_name-1.0.0.tar.gz"
        echo "请将应用包放在当前目录或 /export/source/ 目录下"
        return 1
    fi

    # 解压应用包
    echo "解压应用包..."
    if [ -f "/export/source/$service_name-1.0.0.tar.gz" ]; then
        tar -xzf "/export/source/$service_name-1.0.0.tar.gz" -C /export/app/
    else
        tar -xzf "$service_name-1.0.0.tar.gz" -C /export/app/
    fi

    # 配置 application.properties
    echo "创建配置文件..."
    cat > /export/app/$service_name/config/application.properties << EOF
# ==========================================
# $service_name 服务配置文件
# 部署时间: $(date)
# ==========================================

# Server Configuration
server.port=$port
server.servlet.context-path=/$service_name
server.tomcat.max-threads=200
server.tomcat.min-spare-threads=10

# Database Configuration (TDSQL)
spring.datasource.url=jdbc:mysql://your-tdsql-host:3306/everisk?useSSL=false&serverTimezone=Asia/Shanghai&characterEncoding=utf8&useUnicode=true
spring.datasource.username=everisk
spring.datasource.password=everisk123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# Kafka Configuration
spring.kafka.bootstrap-servers=sh.bdc_tom.a:9092,sh.bdc_tom.b:9092,hl.bdc_tom.a:9092
spring.kafka.consumer.group-id=$service_name-group
spring.kafka.consumer.auto-offset-reset=latest
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.producer.retries=3
spring.kafka.producer.batch-size=16384
spring.kafka.producer.buffer-memory=********

# Redis Configuration
spring.redis.host=sh.bdc_tom.a
spring.redis.port=6379
spring.redis.password=redis123
spring.redis.timeout=3000
spring.redis.jedis.pool.max-active=20
spring.redis.jedis.pool.max-idle=10
spring.redis.jedis.pool.min-idle=5

# Logging Configuration
logging.config=/export/app/$service_name/config/log4j2.xml
logging.level.root=INFO
logging.level.com.dongguanbank=DEBUG
logging.file.path=/export/app/$service_name/log

# Management Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.server.port=$((port + 1000))

# Application Specific Configuration
app.name=$service_name
app.version=1.0.0
app.environment=production
EOF

    # 创建启动脚本
    echo "创建启动脚本..."
    cat > /export/app/$service_name/bin/service.sh << 'EOF'
#!/bin/bash

# ==========================================
# SERVICE_NAME 服务管理脚本
# 部署用户: appuser
# 部署目录: /export
# ==========================================

# 服务配置
SERVICE_NAME="SERVICE_NAME_PLACEHOLDER"
APP_HOME="/export/app/\$SERVICE_NAME"
JAVA_HOME="/export/tools/jdk"
PID_FILE="\$APP_HOME/\$SERVICE_NAME.pid"
JAR_FILE="\$APP_HOME/lib/\$SERVICE_NAME.jar"
LOG_FILE="\$APP_HOME/log/\$SERVICE_NAME.log"
GC_LOG_FILE="\$APP_HOME/log/gc-\$SERVICE_NAME.log"

# JVM 参数配置
MEMORY_SIZE="MEMORY_PLACEHOLDER"
JAVA_OPTS="-Xmx\$MEMORY_SIZE -Xms\$MEMORY_SIZE"
JAVA_OPTS="\$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="\$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="\$JAVA_OPTS -XX:+PrintGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
JAVA_OPTS="\$JAVA_OPTS -Xloggc:\$GC_LOG_FILE"
JAVA_OPTS="\$JAVA_OPTS -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M"
JAVA_OPTS="\$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="\$JAVA_OPTS -XX:HeapDumpPath=\$APP_HOME/log/"
JAVA_OPTS="\$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="\$JAVA_OPTS -Dspring.config.location=\$APP_HOME/config/application.properties"
JAVA_OPTS="\$JAVA_OPTS -Dlogging.config=\$APP_HOME/config/log4j2.xml"
JAVA_OPTS="\$JAVA_OPTS -Duser.timezone=Asia/Shanghai"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "\${GREEN}[INFO]\${NC} \$1"
}

log_warn() {
    echo -e "\${YELLOW}[WARN]\${NC} \$1"
}

log_error() {
    echo -e "\${RED}[ERROR]\${NC} \$1"
}

# 检查进程是否运行
is_running() {
    if [ -f "\$PID_FILE" ]; then
        local pid=\$(cat "\$PID_FILE")
        if ps -p "\$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "\$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动服务
start_service() {
    log_info "启动 \$SERVICE_NAME 服务..."

    if is_running; then
        log_warn "\$SERVICE_NAME 服务已在运行 (PID: \$(cat \$PID_FILE))"
        return 1
    fi

    # 检查必要文件
    if [ ! -f "\$JAR_FILE" ]; then
        log_error "JAR 文件不存在: \$JAR_FILE"
        return 1
    fi

    if [ ! -f "\$JAVA_HOME/bin/java" ]; then
        log_error "Java 可执行文件不存在: \$JAVA_HOME/bin/java"
        return 1
    fi

    # 创建日志目录
    mkdir -p "\$(dirname "\$LOG_FILE")"

    # 切换到应用目录
    cd "\$APP_HOME" || {
        log_error "无法切换到应用目录: \$APP_HOME"
        return 1
    }

    # 启动应用
    log_info "执行命令: \$JAVA_HOME/bin/java \$JAVA_OPTS -jar \$JAR_FILE"
    nohup "\$JAVA_HOME/bin/java" \$JAVA_OPTS -jar "\$JAR_FILE" > "\$LOG_FILE" 2>&1 &
    local pid=\$!

    # 保存 PID
    echo "\$pid" > "\$PID_FILE"

    # 等待服务启动
    sleep 3

    if is_running; then
        log_info "\$SERVICE_NAME 服务启动成功 (PID: \$pid)"
        return 0
    else
        log_error "\$SERVICE_NAME 服务启动失败"
        return 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止 \$SERVICE_NAME 服务..."

    if ! is_running; then
        log_warn "\$SERVICE_NAME 服务未运行"
        return 0
    fi

    local pid=\$(cat "\$PID_FILE")
    log_info "正在停止进程 \$pid..."

    # 优雅停止
    kill "\$pid"

    # 等待进程结束
    local count=0
    while [ \$count -lt 30 ]; do
        if ! ps -p "\$pid" > /dev/null 2>&1; then
            rm -f "\$PID_FILE"
            log_info "\$SERVICE_NAME 服务已停止"
            return 0
        fi
        sleep 1
        count=\$((count + 1))
    done

    # 强制停止
    log_warn "优雅停止超时，强制停止进程..."
    kill -9 "\$pid" 2>/dev/null
    rm -f "\$PID_FILE"
    log_info "\$SERVICE_NAME 服务已强制停止"
    return 0
}

# 重启服务
restart_service() {
    log_info "重启 \$SERVICE_NAME 服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
status_service() {
    if is_running; then
        local pid=\$(cat "\$PID_FILE")
        log_info "\$SERVICE_NAME 服务运行中 (PID: \$pid)"

        # 显示端口信息
        local port=\$(netstat -tlnp 2>/dev/null | grep "\$pid" | awk '{print \$4}' | cut -d: -f2 | head -1)
        if [ -n "\$port" ]; then
            log_info "监听端口: \$port"
        fi

        # 显示内存使用
        local memory=\$(ps -p "\$pid" -o rss= 2>/dev/null | awk '{print int(\$1/1024)"MB"}')
        if [ -n "\$memory" ]; then
            log_info "内存使用: \$memory"
        fi

        return 0
    else
        log_error "\$SERVICE_NAME 服务未运行"
        return 1
    fi
}

# 查看日志
logs_service() {
    if [ -f "\$LOG_FILE" ]; then
        tail -f "\$LOG_FILE"
    else
        log_error "日志文件不存在: \$LOG_FILE"
        return 1
    fi
}

# 主函数
case "\$1" in
    start)
        start_service
        ;;
    stop)
        stop_service
        ;;
    restart)
        restart_service
        ;;
    status)
        status_service
        ;;
    logs)
        logs_service
        ;;
    *)
        echo "用法: \$0 {start|stop|restart|status|logs}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务"
        echo "  stop    - 停止服务"
        echo "  restart - 重启服务"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看服务日志"
        exit 1
        ;;
esac

exit \$?
EOF

    # 替换模板中的占位符
    sed -i "s/SERVICE_NAME_PLACEHOLDER/$service_name/g" /export/app/$service_name/bin/service.sh
    sed -i "s/MEMORY_PLACEHOLDER/$memory/g" /export/app/$service_name/bin/service.sh

    # 创建日志配置文件
    echo "创建日志配置文件..."
    cat > /export/app/$service_name/config/log4j2.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_HOME">/export/app/SERVICE_NAME_PLACEHOLDER/log</Property>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n</Property>
    </Properties>

    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <!-- 文件输出 -->
        <RollingFile name="FileAppender" fileName="${LOG_HOME}/SERVICE_NAME_PLACEHOLDER.log"
                     filePattern="${LOG_HOME}/SERVICE_NAME_PLACEHOLDER.%d{yyyy-MM-dd}.%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>

        <!-- 错误日志 -->
        <RollingFile name="ErrorAppender" fileName="${LOG_HOME}/SERVICE_NAME_PLACEHOLDER-error.log"
                     filePattern="${LOG_HOME}/SERVICE_NAME_PLACEHOLDER-error.%d{yyyy-MM-dd}.%i.log.gz">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- 应用日志 -->
        <Logger name="com.dongguanbank" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="ErrorAppender"/>
        </Logger>

        <!-- Spring 框架日志 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- Kafka 日志 -->
        <Logger name="org.apache.kafka" level="WARN" additivity="false">
            <AppenderRef ref="FileAppender"/>
        </Logger>

        <!-- 根日志 -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="FileAppender"/>
            <AppenderRef ref="ErrorAppender"/>
        </Root>
    </Loggers>
</Configuration>
EOF

    # 替换日志配置中的占位符
    sed -i "s/SERVICE_NAME_PLACEHOLDER/$service_name/g" /export/app/$service_name/config/log4j2.xml

    # 设置权限
    chmod +x /export/app/$service_name/bin/service.sh
    chown -R appuser:appuser /export/app/$service_name

    # 创建符号链接到管理目录
    ln -sf /export/app/$service_name/bin/service.sh /export/bin/$service_name-service.sh 2>/dev/null || true

    echo "=========================================="
    echo "✅ $service_name 服务部署完成"
    echo "服务目录: /export/app/$service_name"
    echo "启动命令: /export/app/$service_name/bin/service.sh start"
    echo "管理命令: /export/bin/$service_name-service.sh {start|stop|restart|status|logs}"
    echo "=========================================="

    return 0
}

# 部署各个应用服务
echo "开始部署东莞银行风险管控系统应用服务..."

# 确保应用包存在
if [ ! -d "/export/source" ]; then
    echo "创建源码目录..."
    mkdir -p /export/source
    echo "请将应用包上传到 /export/source/ 目录"
fi

# 部署服务（按启动顺序）
deploy_app_service "init" "8081" "1g"
deploy_app_service "receiver" "8082" "1g"
deploy_app_service "cleaner" "8083" "2g"
deploy_app_service "transfer" "8080" "2g"
deploy_app_service "threat" "8084" "4g"
deploy_app_service "web-service" "8085" "2g"

echo "=========================================="
echo "🎉 所有应用服务部署完成！"
echo "=========================================="
echo "部署的服务列表："
echo "  - init        (端口: 8081, 内存: 1g)"
echo "  - receiver    (端口: 8082, 内存: 1g)"
echo "  - cleaner     (端口: 8083, 内存: 2g)"
echo "  - transfer    (端口: 8080, 内存: 2g)"
echo "  - threat      (端口: 8084, 内存: 4g)"
echo "  - web-service (端口: 8085, 内存: 2g)"
echo "=========================================="
echo "下一步操作："
echo "1. 检查 TDSQL 数据库连接配置"
echo "2. 启动 Zookeeper 和 Kafka 服务"
echo "3. 使用 manage_services_verbose.sh 启动应用服务"
echo "4. 验证服务状态和端口监听"
echo "=========================================="
```

### 4.3 服务验证和测试
```bash
# 验证部署结果
echo "验证应用服务部署..."

for service in init receiver cleaner transfer threat web-service; do
    echo "检查 $service 服务..."

    # 检查目录结构
    if [ -d "/export/app/$service" ]; then
        echo "✅ $service 目录存在"
    else
        echo "❌ $service 目录不存在"
        continue
    fi

    # 检查启动脚本
    if [ -x "/export/app/$service/bin/service.sh" ]; then
        echo "✅ $service 启动脚本可执行"
    else
        echo "❌ $service 启动脚本不可执行"
    fi

    # 检查配置文件
    if [ -f "/export/app/$service/config/application.properties" ]; then
        echo "✅ $service 配置文件存在"
    else
        echo "❌ $service 配置文件不存在"
    fi

    echo "---"
done

echo "验证完成！"
echo "验证应用服务部署..."

for service in init receiver cleaner transfer threat web-service; do
    echo "检查 $service 服务..."

    # 检查目录结构
    if [ -d "/export/app/$service" ]; then
        echo "✅ $service 目录存在"
    else
        echo "❌ $service 目录不存在"
        continue
    fi

    # 检查启动脚本
    if [ -x "/export/app/$service/bin/service.sh" ]; then
        echo "✅ $service 启动脚本可执行"
    else
        echo "❌ $service 启动脚本不可执行"
    fi

    # 检查配置文件
    if [ -f "/export/app/$service/config/application.properties" ]; then
        echo "✅ $service 配置文件存在"
    else
        echo "❌ $service 配置文件不存在"
    fi

    echo "---"
done

echo "验证完成！"
```

## 🔄 第五阶段：统一服务管理

### 5.1 创建统一管理脚本
```bash
# 创建 /export/bin/manage_services_verbose.sh
cat > /export/bin/manage_services.sh << 'EOF'
#!/bin/bash

# 配置信息
EXPORT_DIR="/export"
LOG_FILE="/export/logs/service_manager.log"
USER="appuser"

# 服务配置 (服务名:目录:类型)
SERVICES=(
    "zookeeper:server/zookeeper:server"
    "kafka:server/kafka:server"
    "init:app/init:app"
    "receiver:app/receiver:app"
    "cleaner:app/cleaner:app"
    "transfer:app/transfer:app"
    "threat:app/threat:app"
    "web-service:app/web-service:app"
)

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 执行服务操作
execute_service_action() {
    local service_name=$1
    local service_dir=$2
    local action=$3

    local full_path="$EXPORT_DIR/$service_dir"
    local script_path="$full_path/bin/service.sh"

    log_message "========================================"
    log_message "Processing $service_name"
    log_message "Service directory: $full_path"
    log_message "Action: $action"
    log_message "----------------------------------------"

    if [ ! -d "$full_path" ]; then
        log_message "ERROR: Service directory not found: $full_path"
        return 1
    fi

    if [ ! -f "$script_path" ]; then
        log_message "ERROR: Service script not found: $script_path"
        return 1
    fi

    # 切换到服务目录并执行
    cd "$full_path" || return 1

    log_message "Executing: ./bin/service.sh $action"

    # 创建临时文件捕获输出
    local temp_output=$(mktemp)

    # 执行命令并捕获输出
    if sudo -u "$USER" ./bin/service.sh "$action" 2>&1 | tee "$temp_output"; then
        log_message "✓ $service_name $action succeeded."
        cat "$temp_output" >> "$LOG_FILE"
        rm -f "$temp_output"
        return 0
    else
        local exit_code=$?
        log_message "✗ $service_name $action failed (exit code: $exit_code)"
        log_message "Error output:"
        cat "$temp_output" | tee -a "$LOG_FILE"
        rm -f "$temp_output"
        return $exit_code
    fi
}

# 主函数
main() {
    local action=$1
    local target_service=$2

    if [ -z "$action" ]; then
        echo "Usage: $0 {start|stop|restart|status} [service_name]"
        echo "Available services:"
        for service_config in "${SERVICES[@]}"; do
            local service_name=$(echo "$service_config" | cut -d: -f1)
            echo "  - $service_name"
        done
        exit 1
    fi

    # 创建日志目录
    mkdir -p "$(dirname "$LOG_FILE")"

    log_message "Starting $action operation..."

    local failed_services=()
    local success_count=0

    for service_config in "${SERVICES[@]}"; do
        local service_name=$(echo "$service_config" | cut -d: -f1)
        local service_dir=$(echo "$service_config" | cut -d: -f2)
        local service_type=$(echo "$service_config" | cut -d: -f3)

        # 如果指定了特定服务，只处理该服务
        if [ -n "$target_service" ] && [ "$target_service" != "$service_name" ]; then
            continue
        fi

        if execute_service_action "$service_name" "$service_dir" "$action"; then
            ((success_count++))
            # 服务启动后等待一段时间
            if [ "$action" = "start" ]; then
                sleep 3
            fi
        else
            failed_services+=("$service_name")
        fi
    done

    log_message "========================================"
    log_message "Operation completed: $action"
    log_message "Successful: $success_count"
    log_message "Failed: ${#failed_services[@]}"

    if [ ${#failed_services[@]} -gt 0 ]; then
        log_message "Failed services: ${failed_services[*]}"
        exit 1
    else
        log_message "All services $action completed successfully"
        exit 0
    fi
}

# 执行主函数
main "$@"
EOF

chmod +x /export/bin/manage_services.sh
chown appuser:appuser /export/bin/manage_services.sh
```

### 5.2 创建服务检查脚本
```bash
# 创建 /export/bin/check_services.sh
cat > /export/bin/check_services.sh << 'EOF'
#!/bin/bash

echo "=== 东莞银行风险管控系统服务状态检查 ==="
echo "检查时间: $(date)"
echo

# 检查基础服务
echo "=== 基础服务检查 ==="
echo "Zookeeper:"
if command -v nc >/dev/null 2>&1; then
    echo stat | nc localhost 2181 2>/dev/null | grep Mode || echo "  状态: 未运行或连接失败"
else
    echo "  警告: nc 命令未安装，无法检查 Zookeeper 状态"
fi

echo "Kafka:"
if [ -f /export/server/kafka/kafka/bin/kafka-topics.sh ]; then
    /export/server/kafka/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092 2>/dev/null | head -5 || echo "  状态: 未运行或连接失败"
else
    echo "  警告: Kafka 未安装"
fi

# 检查数据库服务
echo
echo "=== 数据库服务检查 ==="
echo "PostgreSQL:"
if systemctl is-active postgresql-13 >/dev/null 2>&1; then
    echo "  状态: 运行中"
else
    echo "  状态: 未运行"
fi

echo "Redis:"
if redis-cli ping >/dev/null 2>&1; then
    echo "  状态: 运行中"
else
    echo "  状态: 未运行或连接失败"
fi

# 检查应用服务
echo
echo "=== 应用服务检查 ==="
for service in init receiver cleaner transfer threat web-service; do
    echo "$service:"
    if [ -f "/export/app/$service/bin/service.sh" ]; then
        /export/app/$service/bin/service.sh status 2>/dev/null || echo "  状态: 未运行"
    else
        echo "  状态: 未安装"
    fi
done

# 检查端口监听
echo
echo "=== 端口监听检查 ==="
echo "检查关键端口..."
for port in 2181 9092 5432 6379 8080 8081 8082 8083 8084 8085; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "  端口 $port: 监听中"
    else
        echo "  端口 $port: 未监听"
    fi
done

# 检查进程
echo
echo "=== 进程检查 ==="
echo "Java 进程:"
ps aux | grep java | grep appuser | grep -v grep | wc -l | xargs echo "  运行中的 Java 进程数:"

echo
echo "=== 检查完成 ==="
EOF

chmod +x /export/bin/check_services.sh
chown appuser:appuser /export/bin/check_services.sh
```

## 🔍 第六阶段：服务启动和验证

### 6.1 服务启动顺序
```bash
# 创建启动脚本 /export/bin/start_all_services.sh
cat > /export/bin/start_all_services.sh << 'EOF'
#!/bin/bash

echo "=== 东莞银行风险管控系统启动脚本 ==="
echo "启动时间: $(date)"

# 1. 启动基础服务
echo
echo "第一步: 启动基础服务..."
echo "启动 Zookeeper..."
/export/bin/manage_services.sh start zookeeper
sleep 10

echo "启动 Kafka..."
/export/bin/manage_services.sh start kafka
sleep 5

# 2. 启动数据库服务
echo
echo "第二步: 启动数据库服务..."
echo "启动 PostgreSQL..."
systemctl start postgresql-13

echo "启动 Redis..."
systemctl start redis

# 3. 启动应用服务
echo
echo "第三步: 启动应用服务..."
for service in init receiver cleaner transfer threat web-service; do
    echo "启动 $service..."
    /export/bin/manage_services.sh start $service
    sleep 3
done

echo
echo "=== 所有服务启动完成 ==="
echo "执行服务检查..."
/export/bin/check_services.sh
EOF

chmod +x /export/bin/start_all_services.sh
chown appuser:appuser /export/bin/start_all_services.sh
```

### 6.2 验证部署
```bash
# 验证部署完整性
echo "=== 验证部署完整性 ==="

# 1. 检查目录结构
echo "检查目录结构..."
for dir in tools app bin config data logs server source; do
    if [ -d "/export/$dir" ]; then
        echo "✓ /export/$dir 存在"
    else
        echo "✗ /export/$dir 不存在"
    fi
done

# 2. 检查用户权限
echo "检查用户权限..."
if id appuser >/dev/null 2>&1; then
    echo "✓ 用户 appuser 存在"
    echo "  用户组: $(groups appuser)"
else
    echo "✗ 用户 appuser 不存在"
fi

# 3. 检查 Java 环境
echo "检查 Java 环境..."
if [ -d "/export/tools/jdk" ]; then
    echo "✓ JDK 8 安装在 /export/tools/jdk"
    /export/tools/jdk/bin/java -version
fi

if [ -d "/export/tools/jdk-17" ]; then
    echo "✓ JDK 17 安装在 /export/tools/jdk-17"
    /export/tools/jdk-17/bin/java -version
fi

# 4. 检查服务脚本
echo "检查服务脚本..."
for script in manage_services.sh check_services.sh start_all_services.sh; do
    if [ -x "/export/bin/$script" ]; then
        echo "✓ /export/bin/$script 可执行"
    else
        echo "✗ /export/bin/$script 不可执行或不存在"
    fi
done
```

## 🚨 第七阶段：故障排除指南

### 7.1 常见问题解决

#### 问题1: 服务启动失败
```bash
# 检查日志
tail -f /export/logs/service_manager.log

# 检查具体服务日志
tail -f /export/app/[service_name]/log/[service_name].log

# 检查端口占用
netstat -tlnp | grep [port]

# 检查进程状态
ps aux | grep [service_name]
```

#### 问题2: 数据库连接失败
```bash
# 检查 PostgreSQL 状态
systemctl status postgresql-13

# 测试数据库连接
psql -h localhost -U everisk -d everisk

# 检查防火墙
firewall-cmd --list-all
```

#### 问题3: Kafka 连接问题
```bash
# 检查 Kafka 主题
/export/server/kafka/kafka/bin/kafka-topics.sh --list --bootstrap-server localhost:9092

# 检查 Zookeeper 连接
echo stat | nc localhost 2181

# 检查 Kafka 日志
tail -f /export/server/kafka/data/kafka-logs/server.log
```

### 7.2 性能监控脚本
```bash
# 创建性能监控脚本
cat > /export/bin/monitor_performance.sh << 'EOF'
#!/bin/bash

echo "=== 系统性能监控 ==="
echo "监控时间: $(date)"

# CPU 使用率
echo
echo "=== CPU 使用率 ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print "CPU 使用率: " $1 "%"}'

# 内存使用情况
echo
echo "=== 内存使用情况 ==="
free -h | grep Mem | awk '{print "总内存: " $2 ", 已用: " $3 ", 可用: " $7}'

# 磁盘使用情况
echo
echo "=== 磁盘使用情况 ==="
df -h /export | tail -1 | awk '{print "/export 分区: 总大小 " $2 ", 已用 " $3 " (" $5 "), 可用 " $4}'

# Java 进程内存使用
echo
echo "=== Java 进程内存使用 ==="
ps aux | grep java | grep appuser | grep -v grep | awk '{print $11 " - PID: " $2 ", 内存: " $4 "%"}'

# 网络连接状态
echo
echo "=== 网络连接状态 ==="
echo "监听端口数: $(netstat -tln | grep LISTEN | wc -l)"
echo "已建立连接数: $(netstat -tn | grep ESTABLISHED | wc -l)"

# 服务状态摘要
echo
echo "=== 服务状态摘要 ==="
for port in 2181 9092 5432 6379 8080 8081 8082 8083 8084 8085; do
    if netstat -tln | grep ":$port " >/dev/null; then
        echo "端口 $port: ✓"
    else
        echo "端口 $port: ✗"
    fi
done
EOF

chmod +x /export/bin/monitor_performance.sh
chown appuser:appuser /export/bin/monitor_performance.sh
```

## 📋 第八阶段：部署检查清单

### 8.1 部署前检查
- [ ] 系统环境准备完成 (防火墙、SELinux、系统参数)
- [ ] 用户 `appuser` 创建完成，权限配置正确
- [ ] `/export` 目录结构创建完成
- [ ] JDK 8 和 JDK 17 安装完成
- [ ] 环境变量配置正确

### 8.2 基础服务检查
- [ ] Zookeeper 集群部署完成，配置正确
- [ ] Kafka 集群部署完成，配置正确
- [ ] PostgreSQL 数据库安装配置完成
- [ ] Redis 缓存服务安装配置完成

### 8.3 应用服务检查
- [ ] init 服务 (端口 8081) 部署完成
- [ ] receiver 服务 (端口 8082) 部署完成
- [ ] cleaner 服务 (端口 8083) 部署完成
- [ ] transfer 服务 (端口 8080) 部署完成
- [ ] threat 服务 (端口 8084) 部署完成
- [ ] web-service 服务 (端口 8085) 部署完成

### 8.4 管理脚本检查
- [ ] 统一服务管理脚本 `/export/bin/manage_services.sh` 创建完成
- [ ] 服务检查脚本 `/export/bin/check_services.sh` 创建完成
- [ ] 启动脚本 `/export/bin/start_all_services.sh` 创建完成
- [ ] 性能监控脚本 `/export/bin/monitor_performance.sh` 创建完成

## 🔄 第九阶段：开机自启动配置

### 9.1 配置 rc.local 自启动
```bash
# 首先创建专门的启动脚本
cat > /export/bin/system_startup.sh << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动脚本

# 等待系统完全启动
sleep 30

# 设置日志文件
STARTUP_LOG="/export/logs/system_startup.log"
mkdir -p /export/logs

echo "=========================================" >> $STARTUP_LOG
echo "$(date) - 系统启动，开始启动服务..." >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

# 启动基础服务
echo "$(date) - 启动 Redis..." >> $STARTUP_LOG
systemctl start redis >> $STARTUP_LOG 2>&1

# 等待基础服务启动
sleep 10

# 启动 Zookeeper
echo "$(date) - 启动 Zookeeper..." >> $STARTUP_LOG
if [ -f "/export/server/zookeeper/bin/service.sh" ]; then
    su - appuser -c "/export/server/zookeeper/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 15
fi

# 启动 Kafka
echo "$(date) - 启动 Kafka..." >> $STARTUP_LOG
if [ -f "/export/server/kafka/bin/service.sh" ]; then
    su - appuser -c "/export/server/kafka/bin/service.sh start" >> $STARTUP_LOG 2>&1
    sleep 10
fi

# 启动应用服务
echo "$(date) - 启动应用服务..." >> $STARTUP_LOG
if [ -f "/export/bin/manage_services_verbose.sh" ]; then
    su - appuser -c "/export/bin/manage_services_verbose.sh start" >> $STARTUP_LOG 2>&1
fi

echo "$(date) - 所有服务启动完成" >> $STARTUP_LOG
echo "=========================================" >> $STARTUP_LOG

# 发送启动完成通知（可选）
# echo "东莞银行风险管控系统启动完成" | mail -s "系统启动通知" <EMAIL>

exit 0
EOF

# 设置启动脚本权限
chmod +x /export/bin/system_startup.sh
chown appuser:appuser /export/bin/system_startup.sh

# 然后在 rc.local 中简单调用启动脚本
cat > /etc/rc.d/rc.local << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 调用专门的启动脚本
/export/bin/system_startup.sh &

exit 0
EOF

# 设置执行权限
chmod +x /etc/rc.d/rc.local

# 启用 rc-local 服务（CentOS 7/8）
systemctl enable rc-local
systemctl start rc-local

echo "开机自启动配置完成"
```

### 9.2 配置 systemd 服务自启动（推荐）

#### 方案一：创建独立的 systemd 服务
```bash
# 创建 systemd 服务文件
cat > /etc/systemd/system/dongguanbank-risk.service << 'EOF'
[Unit]
Description=东莞银行风险管控系统
After=network.target multi-user.target
Wants=network.target

[Service]
Type=forking
User=root
ExecStart=/export/bin/start_all_services.sh
ExecStop=/export/bin/manage_services_verbose.sh stop
ExecReload=/export/bin/manage_services_verbose.sh restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=no
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 配置
systemctl daemon-reload

# 启用服务
systemctl enable dongguanbank-risk.service

echo "systemd 自启动服务配置完成"
```

#### 方案二：通过 rc.local 启动 systemd 服务
```bash
# 首先创建 systemd 服务文件（同上）
cat > /etc/systemd/system/dongguanbank-risk.service << 'EOF'
[Unit]
Description=东莞银行风险管控系统
After=network.target multi-user.target
Wants=network.target

[Service]
Type=forking
User=root
ExecStart=/export/bin/start_all_services.sh
ExecStop=/export/bin/manage_services_verbose.sh stop
ExecReload=/export/bin/manage_services_verbose.sh restart
TimeoutStartSec=300
TimeoutStopSec=120
Restart=no
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 重新加载 systemd 配置
systemctl daemon-reload

# 注意：不要 enable 服务，而是通过 rc.local 启动
# systemctl enable dongguanbank-risk.service  # 不执行这行

# 修改 rc.local，通过 systemctl 启动服务
cat > /etc/rc.d/rc.local << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 方案选择：
# 方案1：直接调用启动脚本
# /export/bin/system_startup.sh &

# 方案2：通过 systemctl 启动 systemd 服务（推荐）
systemctl start dongguanbank-risk.service

exit 0
EOF

# 设置执行权限
chmod +x /etc/rc.d/rc.local

echo "通过 rc.local 启动 systemd 服务配置完成"
```

#### 方案三：混合模式（灵活配置）
```bash
# 创建灵活的 rc.local 配置
cat > /etc/rc.d/rc.local << 'EOF'
#!/bin/bash
# 东莞银行风险管控系统开机自启动

# 配置启动方式（可根据需要修改）
STARTUP_MODE="systemd"  # 可选值：script, systemd, both

case "$STARTUP_MODE" in
    "script")
        # 直接调用启动脚本
        echo "使用脚本模式启动服务..."
        /export/bin/system_startup.sh &
        ;;
    "systemd")
        # 通过 systemd 服务启动
        echo "使用 systemd 模式启动服务..."
        systemctl start dongguanbank-risk.service
        ;;
    "both")
        # 两种方式都尝试（容错模式）
        echo "使用混合模式启动服务..."
        if systemctl start dongguanbank-risk.service; then
            echo "systemd 服务启动成功"
        else
            echo "systemd 服务启动失败，尝试脚本模式..."
            /export/bin/system_startup.sh &
        fi
        ;;
    *)
        echo "未知启动模式: $STARTUP_MODE"
        # 默认使用脚本模式
        /export/bin/system_startup.sh &
        ;;
esac

exit 0
EOF

# 设置执行权限
chmod +x /etc/rc.d/rc.local

echo "混合模式 rc.local 配置完成"
```

#### 各方案对比

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| **独立 systemd** | 标准化、日志完善、依赖管理 | 配置复杂 | 现代 Linux 系统 |
| **rc.local + systemd** | 兼容性好、可控性强 | 需要两层配置 | 混合环境 |
| **混合模式** | 灵活、容错性好 | 逻辑复杂 | 不确定环境 |

### 9.3 创建优雅关机脚本
```bash
# 创建关机脚本
cat > /export/bin/shutdown_services.sh << 'EOF'
#!/bin/bash

# 优雅关闭所有服务
SHUTDOWN_LOG="/export/logs/system_shutdown.log"

echo "=========================================" >> $SHUTDOWN_LOG
echo "$(date) - 系统关机，开始停止服务..." >> $SHUTDOWN_LOG
echo "=========================================" >> $SHUTDOWN_LOG

# 停止应用服务
echo "$(date) - 停止应用服务..." >> $SHUTDOWN_LOG
if [ -f "/export/bin/manage_services_verbose.sh" ]; then
    su - appuser -c "/export/bin/manage_services_verbose.sh stop" >> $SHUTDOWN_LOG 2>&1
fi

# 等待应用服务完全停止
sleep 10

# 停止 Kafka
echo "$(date) - 停止 Kafka..." >> $SHUTDOWN_LOG
if [ -f "/export/server/kafka/bin/service.sh" ]; then
    su - appuser -c "/export/server/kafka/bin/service.sh stop" >> $SHUTDOWN_LOG 2>&1
    sleep 5
fi

# 停止 Zookeeper
echo "$(date) - 停止 Zookeeper..." >> $SHUTDOWN_LOG
if [ -f "/export/server/zookeeper/bin/service.sh" ]; then
    su - appuser -c "/export/server/zookeeper/bin/service.sh stop" >> $SHUTDOWN_LOG 2>&1
fi

# 停止 Redis
echo "$(date) - 停止 Redis..." >> $SHUTDOWN_LOG
systemctl stop redis >> $SHUTDOWN_LOG 2>&1

echo "$(date) - 所有服务停止完成" >> $SHUTDOWN_LOG
echo "=========================================" >> $SHUTDOWN_LOG

exit 0
EOF

chmod +x /export/bin/shutdown_services.sh

# 配置关机时执行脚本
cat > /etc/systemd/system/dongguanbank-shutdown.service << 'EOF'
[Unit]
Description=东莞银行风险管控系统优雅关机
DefaultDependencies=false
Before=shutdown.target reboot.target halt.target

[Service]
Type=oneshot
ExecStart=/export/bin/shutdown_services.sh
TimeoutStopSec=120

[Install]
WantedBy=halt.target reboot.target shutdown.target
EOF

systemctl daemon-reload
systemctl enable dongguanbank-shutdown.service

echo "优雅关机脚本配置完成"
```

## 🎯 快速部署命令

### 一键启动所有服务
```bash
sudo /export/bin/start_all_services.sh
```

### 检查所有服务状态
```bash
/export/bin/check_services.sh
```

### 性能监控
```bash
/export/bin/monitor_performance.sh
```

### 单独管理服务
```bash
# 启动单个服务
/export/bin/manage_services_verbose.sh start -s [service_name]

# 停止单个服务
/export/bin/manage_services_verbose.sh stop -s [service_name]

# 重启单个服务
/export/bin/manage_services_verbose.sh restart -s [service_name]

# 查看单个服务状态
/export/bin/manage_services_verbose.sh status -s [service_name]
```

### 系统服务管理
```bash
# 启动系统服务
systemctl start dongguanbank-risk.service

# 停止系统服务
systemctl stop dongguanbank-risk.service

# 查看服务状态
systemctl status dongguanbank-risk.service

# 查看启动日志
journalctl -u dongguanbank-risk.service -f
```

---
