---
#- tags: install_threat

- import_tasks: createUser.yml

- name: 安装tools-es包 #解压压缩包，unarchive模块
  unarchive:
    src: "{{ toolses_tarball_name }}"
    dest: "/home/<USER>/app"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  notify:
    - restart tools-es

# - name: 调整tools-es的启动用户
#   lineinfile: 
#     dest: "/home/<USER>/app/tools-es/bin/service.sh"
#     regexp: "^START_USER="
#     line: "START_USER={{ bangcle_user }}"
    
- name: 配置tools-es的配置文件 
  template:
    src: "tools-es/{{ item.key }}"
    dest: "/home/<USER>/app/tools-es/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'application.properties.j2' , value: 'config/application.properties' }
    - { key: 'kafka-consumer.properties.j2' , value: 'config/kafka-consumer.properties' }
    - { key: 'kafka-producer.properties.j2' , value: 'config/kafka-producer.properties' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }

- name: 提前重启服务
  meta: flush_handlers