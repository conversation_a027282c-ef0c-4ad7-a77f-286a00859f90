---
# tags: install_ELK

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: 安装ELK包
  unarchive:
    src: "{{ elk_tarball_name }}"
    dest: "/home/<USER>/ops/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  notify:
    - restart elk

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/ops/elk/elasticsearch/data"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0777
    recurse: yes
    state: directory

- name: load ELK image
  command: "docker load -i /home/<USER>/ops/elk/images/{{ item }}"
  with_items: 
    - elasticsearch.tar
    - kibana.tar
    - logstash.tar

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/ops/elk/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - logs

# - name: 创建部署所需要的日志
#   file:
#     path: "/home/<USER>/ops/nginx/{{ item }}"
#     owner: "{{ bangcle_user }}"
#     group: "{{ bangcle_user }}"
#     force: yes
#     mode: 0755
#     # recurse: yes
#     state: touch
#   with_items:
#     - logs/error.log

- name: crontab job to clean es when index > one month (30 days)
  become_user: "{{ bangcle_user }}"
  cron:
    name: "clean es job"
    hour: "0"
    user: "{{ bangcle_user }}"
    job: "/home/<USER>/ops/elk/bin/clean.sh > /tmp/elk_clean.txt"

- name: 配置elk服务配置文件及脚本
  template:
    src: "elk/{{ item.key }}"
    dest: "/home/<USER>/ops/elk/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'elasticsearch/elasticsearch.yml.j2' , value: 'elasticsearch/config/elasticsearch.yml' }
    - { key: 'kibana/kibana.yml.j2' , value: 'kibana/config/kibana.yml' }
    - { key: 'logstash/config/logstash.yml.j2' , value: 'logstash/config/logstash.yml' }
    - { key: 'logstash/pipeline/logstash.conf.j2' , value: 'logstash/pipeline/logstash.conf' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'clean.sh.j2' , value: 'bin/clean.sh' }
    - { key: '.env.j2' , value: 'bin/.env' }
    - { key: 'startup_es.sh.j2' , value: 'bin/startup_es.sh' }
    - { key: 'startup_logstash.sh.j2' , value: 'bin/startup_logstash.sh' }
    - { key: 'startup_kibana.sh.j2' , value: 'bin/startup_kibana.sh' }
  # notify:
  #   - restart elk


- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/ops/elk/bin/service.sh"
    path: "/home/<USER>/ops/elk/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/ops/elk/bin/service.sh"
    state: absent
  when: supervisord_used == true

# - name: configure service.sh in supervisor mode
#   template:
#     src: "elk/service_supervisor.sh.j2"
#     dest: "/home/<USER>/ops/elk/bin/service.sh"
#     owner: "{{ bangcle_user }}"
#     group: "{{ bangcle_user }}"
#     mode: 0755
#   when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart elk
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
