---
# tags: deploy_redis

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
# - import_tasks: backupServer.yml
#   vars: 
#     backup_server: api_autotest

- name: 安装api_autotest前端代码
  copy:
    src: "td/api_autotest"
    dest: "/home/<USER>/td/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/td/api_autotest/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - logs
    - config
    - data
    - images

- name: load nginx image
  command: "docker load -i /home/<USER>/td/api_autotest/images/{{ ansible_architecture|lower }}/api_autotest.tar"
  
- name: 配置api_autotest服务配置文件及脚本
  template:
    src: "api_autotest/{{ item.key }}"
    dest: "/home/<USER>/td/api_autotest/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'init.conf.j2' , value: 'config/init.conf' }
