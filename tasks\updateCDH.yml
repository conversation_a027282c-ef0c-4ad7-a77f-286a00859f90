---
# 升级cdh的lib包
- name: check  cdh-jdbc
  stat: 
    path: "/usr/share/cmf/lib/postgresql-9.0-801.jdbc4.jar"
  register: cdh_jdbc
  ignore_errors: True

- name: 备份jdbc包
  command: "mv /usr/share/cmf/lib/postgresql-9.0-801.jdbc4.jar /tmp/postgresql-9.0-801.jdbc4.jar"
  ignore_errors: True
  when: cdh_jdbc.stat.exists == True

- name: 升级jdbc包
  copy:
    src: "{{ cdh_jdbc_path }}"
    dest: /usr/share/cmf/lib/postgresql-42.2.19.jre7.jar
    force: yes
    mode: 0644
  when: cdh_jdbc.stat.exists == True

- name: check  cdh-server
  stat: 
    path: "/etc/init.d/cloudera-scm-server"
  register: cdh_server
  ignore_errors: True

- name: 更新cdh的配置文件，连接新的pg
  shell: "/usr/share/cmf/schema/scm_prepare_database.sh -h {{ groups['postgres'][0] }} postgresql bangcle_cm bangcle_pg beap123"
  ignore_errors: True
  when: cdh_server.stat.exists == True

- name: 重启cm
  command: "/etc/init.d/cloudera-scm-server restart"
  when: cdh_server.stat.exists == True

- name: check  cdh-kafka是否需要升级
  stat: 
    path: "/opt/cloudera/parcel-repo/KAFKA-4.1.0-1.4.1.0.p0.4-el7.parcel"
  register: cdh_kafka
  ignore_errors: True
  

- name: 更新kafka
  copy:
    src: "tools/cdh/update/{{ item }}"
    dest: "/opt/cloudera/parcel-repo/{{ item }}"
    force: yes
    mode: 0644
  with_items:
    - "KAFKA-4.1.0-1.4.1.0.p0.4-el7.parcel.sha"
    - "KAFKA-4.1.0-1.4.1.0.p0.4-el7.parcel"
    - "manifest-KAFKA-4.1.0-1.4.1.0.p0.4.json"
  when: 
    - cdh_kafka.stat.exists != True 
    - cdh_server.stat.exists == True