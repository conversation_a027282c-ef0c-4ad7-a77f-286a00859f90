#!/bin/bash

# 增强版服务管理脚本
# 提供详细的执行过程显示和调试信息

# 设置日志文件目录
LOG_DIR="/export/logs"
LOG_FILE="$LOG_DIR/service_manager_verbose.log"

# 创建日志目录（如果不存在）
mkdir -p "$LOG_DIR"

# 定义服务目录
SERVICE_DIR="/export/app"

# 定义要管理的服务组件列表
MANAGED_SERVICES=(
    "init"
    "receiver"
    "cleaner"
    "transfer"
    "threat"
    "web-service"
    "security-event"
    "analyzer-dev"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status} [选项]"
    echo ""
    echo "操作:"
    echo "  start     启动所有服务"
    echo "  stop      停止所有服务"
    echo "  restart   重启所有服务"
    echo "  status    查看所有服务状态"
    echo ""
    echo "选项:"
    echo "  -v, --verbose     显示详细输出"
    echo "  -d, --debug       启用调试模式"
    echo "  -s, --service     指定单个服务名称"
    echo "  -t, --timeout     设置超时时间（秒，默认30）"
    echo "  -h, --help        显示此帮助信息"
    echo ""
    echo "可管理的服务:"
    for service in "${MANAGED_SERVICES[@]}"; do
        echo "  - $service"
    done
    echo ""
    echo "示例:"
    echo "  $0 start -v                    # 详细模式启动所有服务"
    echo "  $0 status -s init              # 查看init服务状态"
    echo "  $0 restart -d                  # 调试模式重启所有服务"
}

# 解析命令行参数
VERBOSE=false
DEBUG=false
SPECIFIC_SERVICE=""
TIMEOUT=30

while [[ $# -gt 0 ]]; do
    case $1 in
        start|stop|restart|status)
            ACTION=$1
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--debug)
            DEBUG=true
            VERBOSE=true
            shift
            ;;
        -s|--service)
            SPECIFIC_SERVICE="$2"
            shift 2
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查必需参数
if [ -z "$ACTION" ]; then
    echo "错误: 必须指定操作 (start|stop|restart|status)"
    show_help
    exit 1
fi

# 检查服务目录是否存在
if [ ! -d "$SERVICE_DIR" ]; then
    echo -e "${RED}错误: 服务目录 $SERVICE_DIR 不存在${NC}"
    exit 1
fi

# 验证操作参数
case $ACTION in
    start|stop|restart|status)
        ;;
    *)
        echo -e "${RED}错误: 无效的操作: $ACTION${NC}"
        show_help
        exit 1
        ;;
esac

# 日志函数
log_info() {
    local message="$1"
    echo -e "${BLUE}[INFO]${NC} $message" | tee -a $LOG_FILE
}

log_success() {
    local message="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $message" | tee -a $LOG_FILE
}

log_warning() {
    local message="$1"
    echo -e "${YELLOW}[WARNING]${NC} $message" | tee -a $LOG_FILE
}

log_error() {
    local message="$1"
    echo -e "${RED}[ERROR]${NC} $message" | tee -a $LOG_FILE
}

log_debug() {
    local message="$1"
    if [ "$DEBUG" = true ]; then
        echo -e "${PURPLE}[DEBUG]${NC} $message" | tee -a $LOG_FILE
    fi
}

# 检查服务脚本是否存在
check_service_script() {
    local service_dir="$1"
    local service_name="$2"
    
    if [ ! -f "$service_dir/bin/service.sh" ]; then
        log_warning "服务脚本不存在: $service_dir/bin/service.sh"
        return 1
    fi
    
    if [ ! -x "$service_dir/bin/service.sh" ]; then
        log_warning "服务脚本不可执行: $service_dir/bin/service.sh"
        chmod +x "$service_dir/bin/service.sh"
        log_info "已设置执行权限: $service_dir/bin/service.sh"
    fi
    
    return 0
}

# 执行服务操作
execute_service_action() {
    local service_dir="$1"
    local service_name="$2"
    local action="$3"
    
    log_info "开始处理服务: $service_name"
    log_debug "服务目录: $service_dir"
    log_debug "执行操作: $action"
    
    # 检查服务脚本
    if ! check_service_script "$service_dir" "$service_name"; then
        return 1
    fi
    
    # 切换到服务目录
    cd "$service_dir" || {
        log_error "无法切换到服务目录: $service_dir"
        return 1
    }
    
    # 显示即将执行的命令
    local cmd="./bin/service.sh $action"
    log_info "执行命令: $cmd"
    
    # 创建临时文件来捕获输出
    local temp_output=$(mktemp)
    local temp_error=$(mktemp)
    
    # 记录开始时间
    local start_time=$(date +%s)
    
    # 执行命令并捕获输出
    if [ "$VERBOSE" = true ]; then
        echo -e "${CYAN}--- $service_name 执行输出开始 ---${NC}"
        timeout $TIMEOUT ./bin/service.sh $action 2>&1 | tee "$temp_output"
        local exit_code=${PIPESTATUS[0]}
        echo -e "${CYAN}--- $service_name 执行输出结束 ---${NC}"
    else
        timeout $TIMEOUT ./bin/service.sh $action > "$temp_output" 2> "$temp_error"
        local exit_code=$?
    fi
    
    # 记录结束时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 将输出写入日志文件
    echo "=== $service_name 执行输出 ===" >> $LOG_FILE
    cat "$temp_output" >> $LOG_FILE
    if [ -s "$temp_error" ]; then
        echo "=== $service_name 错误输出 ===" >> $LOG_FILE
        cat "$temp_error" >> $LOG_FILE
    fi
    echo "=== 执行时间: ${duration}秒 ===" >> $LOG_FILE
    echo "" >> $LOG_FILE
    
    # 检查执行结果
    if [ $exit_code -eq 124 ]; then
        log_error "$service_name $action 超时 (${TIMEOUT}秒)"
        rm -f "$temp_output" "$temp_error"
        return 1
    elif [ $exit_code -eq 0 ]; then
        log_success "$service_name $action 成功 (耗时: ${duration}秒)"
        rm -f "$temp_output" "$temp_error"
        return 0
    else
        log_error "$service_name $action 失败 (退出码: $exit_code, 耗时: ${duration}秒)"
        if [ "$VERBOSE" = false ] && [ -s "$temp_output" ]; then
            echo -e "${RED}错误输出:${NC}"
            cat "$temp_output"
        fi
        if [ -s "$temp_error" ]; then
            echo -e "${RED}错误详情:${NC}"
            cat "$temp_error"
        fi
        rm -f "$temp_output" "$temp_error"
        return 1
    fi
}

# 主执行逻辑
main() {
    # 记录操作开始时间
    echo "========================================" | tee -a $LOG_FILE
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始执行 $ACTION 操作" | tee -a $LOG_FILE
    if [ -n "$SPECIFIC_SERVICE" ]; then
        echo "目标服务: $SPECIFIC_SERVICE" | tee -a $LOG_FILE
    fi
    echo "详细模式: $VERBOSE" | tee -a $LOG_FILE
    echo "调试模式: $DEBUG" | tee -a $LOG_FILE
    echo "超时设置: ${TIMEOUT}秒" | tee -a $LOG_FILE
    echo "========================================" | tee -a $LOG_FILE
    
    local success_count=0
    local fail_count=0
    local skip_count=0
    
    # 遍历指定的服务组件
    for service_name in "${MANAGED_SERVICES[@]}"; do
        local service_dir="$SERVICE_DIR/$service_name"

        # 如果指定了特定服务，只处理该服务
        if [ -n "$SPECIFIC_SERVICE" ] && [ "$service_name" != "$SPECIFIC_SERVICE" ]; then
            continue
        fi

        # 检查服务目录是否存在
        if [ ! -d "$service_dir" ]; then
            log_warning "跳过 $service_name: 服务目录不存在 ($service_dir)"
            ((skip_count++))
            continue
        fi

        # 检查是否有 bin 目录
        if [ -d "$service_dir/bin" ]; then
            if execute_service_action "$service_dir" "$service_name" "$ACTION"; then
                ((success_count++))
            else
                ((fail_count++))
            fi
        else
            log_warning "跳过 $service_name: 缺少 bin 目录"
            ((skip_count++))
        fi

        # 在服务之间添加间隔
        if [ "$ACTION" = "start" ] || [ "$ACTION" = "restart" ]; then
            sleep 2
        fi
    done
    
    # 记录操作结果统计
    echo "========================================" | tee -a $LOG_FILE
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $ACTION 操作完成" | tee -a $LOG_FILE
    log_success "成功: $success_count 个服务"
    if [ $fail_count -gt 0 ]; then
        log_error "失败: $fail_count 个服务"
    fi
    if [ $skip_count -gt 0 ]; then
        log_warning "跳过: $skip_count 个服务"
    fi
    echo "详细日志: $LOG_FILE" | tee -a $LOG_FILE
    echo "========================================" | tee -a $LOG_FILE
    
    # 根据结果设置退出码
    if [ $fail_count -gt 0 ]; then
        exit 1
    else
        exit 0
    fi
}

# 执行主函数
main
