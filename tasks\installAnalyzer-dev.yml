---
#- tags: install_analyzer-dev

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True
  
- name: check analyzer-dev 是否安装
  stat:
    path: "/home/<USER>/app/analyzer-dev/"
  register: check_server
  ignore_errors: True

- import_tasks: backupWxgzApp.yml
  vars:
    backup_server: analyzer-dev
  when:
    - check_server.stat.exists == True

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/app/analyzer-dev/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp

- name: 传包-analyzer-dev
  copy:
    src: "{{ analyzerDev_tarball_name}}"
    dest: "/home/<USER>/app/analyzer-dev/images"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 加载images
  command: "docker load -i /home/<USER>/app/analyzer-dev/images/analyzer-dev.tar"
  
- name: 配置analyzer-dev的config文件
  template:
    src: "analyzer-dev/{{ item.key }}"
    dest: "/home/<USER>/app/analyzer-dev/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length == 1

- name: 配置analyzer-dev的config文件
  template:
    src: "analyzer-dev/{{ item.key }}"
    dest: "/home/<USER>/app/analyzer-dev/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.multi.j2' , value: 'bin/docker-compose.yml' }
    - { key: '.env.j2' , value: 'bin/.env' }
  when:
    - groups['everisk-deploy'] | length > 1

- name: 配置log4j2
  template:
    src: "log4j2/{{ item.key }}"
    dest: "/home/<USER>/app/analyzer-dev/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'log4j2.xml.j2' , value: 'config/log4j2.xml' }

- name: configure startup.sh
  template:
    src: "analyzer-dev/startup.sh.j2"
    dest: "/home/<USER>/app/analyzer-dev/bin/startup.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/app/analyzer-dev/bin/service.sh"
    path: "/home/<USER>/app/analyzer-dev/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/app/analyzer-dev/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "analyzer-dev/service_supervisor.sh.j2"
    dest: "/home/<USER>/app/analyzer-dev/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

#- name: 解决app的log目录内文件bangcle用户无法读取
#  shell: "sudo chmod 666 /home/<USER>/app/analyzer-dev/log/*"
#  when:
#    - ansible_architecture|lower == "aarch64"
#  ignore_errors: True

- name: ready to notify
  shell: ls
  notify:
    - restart analyzer-dev
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
