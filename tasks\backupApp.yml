---

- name: check  {{ backup_server }}是否安装
  stat: 
    path: "/home/<USER>/app/{{ backup_server }}/bin/.env"
  register: check_server
  ignore_errors: True

- name: check  {{ backup_server }}是否已经备份
  stat: 
#    path: "/home/<USER>/app/{{ backup_server }}/bin/.env-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}.{{ hostvars[inventory_hostname]['ansible_date_time']['epoch'] }}"
    path: "/home/<USER>/app/{{ backup_server }}/bin/.env-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}"
  register: check_server_back
  ignore_errors: True

- name: 备份{{ backup_server }}服务
#  command: "cp -a /home/<USER>/app/{{ backup_server }}/bin/.env /home/<USER>/app/{{ backup_server }}/bin/.env-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}.{{ hostvars[inventory_hostname]['ansible_date_time']['epoch'] }}"
  command: "cp -a /home/<USER>/app/{{ backup_server }}/bin/.env /home/<USER>/app/{{ backup_server }}/bin/.env-{{ hostvars[inventory_hostname]['ansible_date_time']['date'] }}"
  notify:
    - stop {{ backup_server }}
  when: 
    - check_server.stat.exists == True
    - check_server_back.stat.exists != True
  ignore_errors: True

- name: 提前停止服务
  meta: flush_handlers
  when:  
    - check_server.stat.exists == True
    # - check_server_back.stat.exists != True
  ignore_errors: True
