---

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/app"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: create directory
  file:
    path: "/home/<USER>/ops/supervisor/bin"
    state: directory
    recurse: yes
    mode: 0755
    force: yes
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
  
- name: unarchive supervisor package
  unarchive:
    src: "ops/cesi/supervisord/supervisor.tar.gz"
    dest: "/home/<USER>/ops/supervisor"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

- name: create  /var/log/supervisor
  file:
    path: "/var/log/supervisor"
    state: directory
    recurse: yes
    mode: 0755
    force: yes
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"

# - name: create /var/log/supervisor directory
#   shell: mkdir -p /var/log/supervisor
#   become_user: "{{ bangcle_user }}"
#   ignore_errors: true

- name: install supervisor
  command: /home/<USER>/ops/supervisor/.supervisor/install_supervisor.sh /home/<USER>/ops/supervisor

- name: enable supervisord service
  service:
    name: supervisord
    enabled: yes

- name: supervisor.conf template
  template:
    src: "supervisor/supervisord.conf.j2"
    dest: "/home/<USER>/ops/supervisor/etc/supervisord.conf"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0744

- name: delete link file
  file:
    path: /etc/supervisord.conf
    state: absent
  ignore_errors: true

- name: link supervisor.conf
  command: ln -s /home/<USER>/ops/supervisor/etc/supervisord.conf /etc/supervisord.conf

#- name: set fact
#  set_fact:
#    python_version: "{{ ansible_python_version.split('.')[0]|lower }}"

- name: set service_groups
  set_fact:
    service_groups: "{{ group_names|servicetransformlist }}"

- name: set ops_groups
  set_fact:
    ops_groups: "{{ group_names|servicetransformlist }}"

# - name: 输出users
#   debug:
#     msg: "{{ group_names }}"

# - name: 输出users
#   debug:
#     msg: "{{ service_groups }}"

- name: ini template
  template:
    src: "supervisor/app.ini.j2"
    dest: "/home/<USER>/ops/supervisor/etc/supervisord.d/app.ini"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0744

- name: delete unused file
  file:
    path: /home/<USER>/ops/supervisor/run_supervisor.sh
    state: absent
  ignore_errors: true

- name: service script template
  template:
    src: "supervisor/service.sh.j2"
    dest: "/home/<USER>/ops/supervisor/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 修改supervisor目录权限
  command: "chown {{ bangcle_user }}.{{ bangcle_user }} -R /home/<USER>/ops/supervisor"

- name: daemon reload
  systemd:
    daemon-reload: true

- name: start supervisord service
  service:
    name: supervisord
    state: restarted
