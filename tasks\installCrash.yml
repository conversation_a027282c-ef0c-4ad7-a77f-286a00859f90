---
# tags: deploy_crash

- name: check docker.pid
  stat: 
    path: "/run/docker.pid"
  register: dockerpid_output
  
- name: check users
  stat: 
    path: "/home/<USER>/server"
  register: users_output

# - name: 输出users
#   debug:
#     msg: "{{ users_output }}"

- import_tasks: createUser.yml
  when: users_output.stat.exists != True
- import_tasks: dockerEnv.yml
  when: dockerpid_output.stat.exists != True

- name: copy crash 
  copy:
    src: "server/crash"
    dest: "/home/<USER>/server/"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755

- name: 创建部署所需要的目录 
  file:
    path: "/home/<USER>/server/crash/{{ item }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
    recurse: yes
    state: directory
  with_items:
    - bin
    - config
    - images
    - log
    - data
    - tmp

# - name: restart docker
#   command: "systemctl restart docker"

- name: load crash image
  command: "docker load -i /home/<USER>/server/crash/images/{{ ansible_architecture|lower }}/crash.tar"

- name: 配置crash服务配置文件及脚本
  template:
    src: "crash/{{ item.key }}"
    dest: "/home/<USER>/server/crash/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "x86_64"
  # notify:
  #   - restart crash

- name: 配置crash服务配置文件及脚本
  template:
    src: "crash/{{ item.key }}"
    dest: "/home/<USER>/server/crash/{{ item.value }}"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    force: yes
    mode: 0755
  with_items:
    - { key: 'service.sh.j2' , value: 'bin/service.sh' }
    - { key: 'docker-compose_aarch64.yml.j2' , value: 'bin/docker-compose.yml' }
    - { key: 'startup.sh.j2' , value: 'bin/startup.sh' }
  when: ansible_architecture|lower == "aarch64"

- name: rename service.sh if supervisor used
  file:
    src: "/home/<USER>/server/crash/bin/service.sh"
    path: "/home/<USER>/server/crash/bin/preservice.sh"
    state: hard
    force: yes
  when: supervisord_used == true

- name: remove old file if supervisor used
  file:
    path: "/home/<USER>/server/crash/bin/service.sh"
    state: absent
  when: supervisord_used == true

- name: configure service.sh in supervisor mode
  template:
    src: "crash/service_supervisor.sh.j2"
    dest: "/home/<USER>/server/crash/bin/service.sh"
    owner: "{{ bangcle_user }}"
    group: "{{ bangcle_user }}"
    mode: 0755
  when: supervisord_used == true

- name: ready to notify
  shell: ls
  notify:
    - restart crash
  when: supervisord_used == false

- name: 提前重启服务
  meta: flush_handlers
